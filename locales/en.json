{"Common": {"settings": "Settings", "confirm": "Confirm", "cancel": "Cancel", "all": "All", "edit": "Edit", "delete": "Delete", "ok": "OK"}, "Auth": {"login": "<PERSON><PERSON>", "emailNotice": "Enter the Email.", "password": "Password", "repeatPassword": "Repeat Password", "passwordNotice": "Enter the Password.", "passwordError": "Email or password is incorrect.", "registerShot": "Register", "register": "Register", "hasAccount": "Has an account？", "clickToLogin": "<PERSON><PERSON>", "passwordLengthLimit": "Password must be at least 8 characters long.", "passwordNotSame": "The passwords are not the same.", "registerFail": "Registration failed, please try again later.", "openRegisterNotice": "Registration is not open, please contact the administrator.", "adminCodeRequired": "Enter the Admin Code", "setupAdminAccount": "Set up an admin account.", "adminCodeNotice": "The value of the root directory .env file or environment variable ADMIN_CODE is used to set up the administrator account during the initial installation.", "setupNotice": "Please set up the administrator account and password. Once the administrator account is set up, this page will no longer be accessible."}, "Onboarding": {"welcome": "欢迎使用 HiveChat", "subtitle": "创建您的第一个工作区，开始与 AI 助手对话", "workspaceName": "工作区名称", "workspaceNamePlaceholder": "例如：我的工作区", "workspaceNameRequired": "请输入工作区名称", "workspaceNameMinLength": "工作区名称至少需要 2 个字符", "workspaceNameMaxLength": "工作区名称不能超过 50 个字符", "workspaceId": "工作区 ID", "workspaceIdPlaceholder": "例如：my-workspace", "workspaceIdRequired": "请输入工作区 ID", "workspaceIdMinLength": "ID 至少需要 5 个字符", "workspaceIdMaxLength": "ID 不能超过 20 个字符", "workspaceIdPattern": "只能包含小写字母、数字和连字符", "workspaceIdHelp": "访问链接：https://cloud.hivechat.net/", "workspaceIdUnavailable": "不可用", "createWorkspace": "创建工作区", "createSuccess": "工作区创建成功！", "createFailed": "创建工作区失败", "createError": "创建工作区时发生错误", "idUnavailableError": "请选择一个可用的工作区 ID", "teamNotice": "创建工作区后，您可以邀请团队成员加入协作"}, "Chat": {"webSearch": "Web Search", "mcpServer": "MCP Server", "mcpTool": "MCP Tool", "builtInImageGen": "Image Generation", "mcpInput": "Input", "mcpOutput": "Output", "mcpFailed": "Failed", "mcpFinished": "Finished", "mcpCall": "Call", "sTool": "‘s <PERSON>l", "newChat": "New Chat", "login": "<PERSON><PERSON>", "defaultChatName": "New Chat", "recentChat": "Recent Chat", "myBots": "My Bots", "discoverBots": "Discover <PERSON>", "historyNotice": "Chat history will be here.", "rename": "<PERSON><PERSON>", "delete": "Delete", "viewAll": "View All", "editChatName": "<PERSON> Chat Name", "save": "Save", "confirm": "Confirm", "cancel": "Cancel", "chatNameplaceholder": "<PERSON><PERSON>'s Name", "settings": "Settings", "noLlmNotice": "No LLM has been set up, ", "clickToSet": "click here to set", "inputPlaceholder": "Ask me anything...", "notSupportVision": "The current model does not support uploading images.", "welcomeNotice": "how can I help you?", "goodMorning": "Good morning, ", "goodAfternoon": "Good afternoon, ", "goodEvening": "Good evening, ", "goodNight": "Good night, ", "adminPanel": "Admin Panel", "historyChat": "History", "today": "Today", "thisWeek": "This Week", "thisMonth": "This Month", "earlier": "Earlier", "renameChat": "<PERSON> Chat Name", "inputChatName": "Input Chat Name", "deleteCurrentChat": "Delete Current Chat", "deleteCurrentChatDetail": "All records of the current chat will be deleted.", "clearMemory": "Clear Memory", "image": "Image", "unsupportImage": "The current model does not support image uploads.", "maxImageCount": "You can upload up to {maxImages} images.", "mustBeImage": "Please select image file.", "imageSizeLimit": "The image size cannot exceed 5MB.", "clearHistoryMessage": "Clear current session messages", "clearHistoryMessageShort": "Clear Messages", "confirmClearHistoryMessage": "Confirm to clear all messages?", "clearHistoryMessageNotice": "All messages of the current chat will be deleted.", "startNewChat": "New chat", "piece": "records", "contextCleared": "Context has been cleared.", "contentParsingError": "Content parsing error.", "deleteNotice": "Notice", "currentMessageDelete": "The current message will be deleted.", "copy": "Copy", "copySuccess": "Copy successful", "retry": "Retry", "unknownUsage": "Token consumption not retrieved", "apiTimeout": "API connection timed out, please check the network or API address.", "apiKeyError": "API key is incorrect or empty, please contact the administrator for assistance.", "historyMessageCount": "History Message Count", "historyMessageCountAllShot": "All", "historyMessageCountAll": "All", "historyMessageCountNone": "None", "historyMessageCountSpecify": "Specify", "historyMessageCountAllNotice": "All messages will be used as context, resulting in increased token consumption.", "historyMessageCountNoneNotice": "Without context, each message is a new beginning, suitable for temporary conversation needs such as translation.", "historyMessageCountSpecifyNotice": "History message count:", "deleteSuccess": "Deleted successfully", "deleteFail": "Deletion failed", "back": "Back", "notExist": "The bot does not exist or has been deleted.", "source": "Source", "chat": "Cha<PERSON>", "addToChats": "Add to chats", "remove": "Remove", "prompt": "Prompt", "noPrompt": "The bot has no prompt.", "createBot": "Create <PERSON><PERSON>", "botName": "<PERSON><PERSON>'s Name", "botNameNotice": "Enter bot's name", "botDesc": "Bo<PERSON>'s Description", "botDescNotice": "Briefly introduce the scenario and how to use it.", "promptNotice": "Example: You are an experienced language teacher with teaching methods that inspire students' enthusiasm for learning. You excel in using humor and practical examples to make teaching engaging.", "pin": "<PERSON>n", "unPin": "Unpin", "modelNotConfigured": "Model Not Configured", "guideAlertText": "The large model service has not been configured yet, and AI chat is not available. Please contact the administrator for setup.", "thinking": "Thinking...", "thought": "Thought"}, "Settings": {"accountSettings": "Account", "systemSettings": "Advanced", "changePassword": "Change Password", "logout": "Logout", "usage": "Usage", "unlimitedTokens": "Unlimited Tokens", "usedThisMonth": "Used this month", "monthlyLimit": "Monthly limit", "nickname": "Nickname", "workspace": "Workspace", "backToWorkspaceList": "Workspace List", "deleteAllMessagesTitle": "Delete all chat messages.", "deleteAllMessagesDesc": "All chat messages will be cleared and cannot be retrieved afterward.", "deleteAction": "Delete", "deleteSuccess": "Deleted successfully", "oldPassword": "Old Password", "newPassword": "New Password", "repeatNewPassword": "Repeat New Password", "inputPassword": "Input Password", "lengthLimit": "Length cannot be less than 8 characters.", "language": "Language"}, "Admin": {"models": "Model Provider", "defaultModel": "Default Model", "users": "Users", "webSearch": "Web Search", "mcpServers": "MCP Servers", "system": "System", "backHome": "Back Home", "logout": "Logout", "logoutNoticeTitle": "Notice", "logoutNoticeDesc": "Confirm to logout?", "Models": {"modelSettings": "Model Settings", "modelSettingsNotice": "← Please select a large model provider on the left for configuration.", "settings": "Settings", "status": "Status", "enabled": "Enabled", "disabled": "Disabled", "endpoint": "Endpoint", "serviceEndpoint": "Endpoint", "configGuide": "Configuration Guide", "save": "Save", "cancel": "Cancel", "testConnect": "Test Connect", "check": "Test", "checkSuccess": "Success", "checkFail": "Fail", "optional": "Optional", "models": "Models", "confirm": "Confirm", "hide": "<PERSON>de", "addModel": "Add Model", "customModel": "Custom Add a Model", "deleteCustomModel": "Delete This Custom Model", "currentModelWillbeDeleted": "Current Model Will be Deleted", "deleteSuccess": "Deleted successfully", "supportVision": "Support Vision", "supportTool": "Support MCP Tools", "conversationUpTo": "The conversation supports up to", "allAdded": "All models have been added.", "confirmAndRetry": "Confirm and Retry", "close": "Close", "addCustomModel": "Add a Custom Model", "modelId": "Model ID", "modelDisplayName": "Model display name", "modelMaxToken": "Max token", "modelIdNotice": "Please input model ID", "modelDisplayNameNotice": "Please input model display name", "modelMaxTokenNotice": "Please input max token", "okText": "OK", "cancelText": "Cancel", "addModelSuccess": "Added successfully", "saveSuccess": "Success", "saveFailed": "Failed", "selectModelToCheck": "Select a Model", "checkConnectivity": "API Check"}, "Users": {"group": "Group", "userList": "User List", "addUser": "Add User", "role": "Role", "emailNotice": "Please input the Email", "password": "Password", "passwordNotice": "Please input the password", "lengthLimit": "Length cannot be less than 8 characters.", "roleAdmin": "Admin", "roleUser": "User", "registerAt": "Register Time", "action": "Action", "edit": "Edit", "delete": "Delete", "deleteNotice": "Confirm to delete this user?", "deleteUserSuccess": "Deleted successfully", "addUserSuccess": "Added successfully", "updateUserSuccess": "Updated successfully", "editUser": "Edit User Info", "groupManagement": "Group Management", "groupManagementTip": "Use user groups to control which models different users can use.", "addGroup": "Add Group", "groupName": "Group Name", "availableModels": "Available Models", "specificModel": "Specific Model", "deleteConfirmTitle": "Delete Confirmation", "deleteConfirmContent": "Are you sure you want to delete this group? After deletion, all users in this group will be automatically changed to 'Default Group'", "defaultGroup": "Default Group", "defaultGroupCannotDelete": "Default Group cannot be deleted", "deleteGroupSuccess": "Deleted successfully"}, "Search": {"webSearch": "Web Search", "enableWebSearch": "Enable Web Search", "enableWebSearchNotice": "When enabled, the AI answers based on search engine results.", "webSearchProvider": "Web SearchProvider", "apikey": "API Key", "getApikey": "Get API Key", "general": "General", "searchResult": "Number of search results", "defaultCount": "default", "apikeyRequiredNotice": "Please enter API key", "check": "Check"}, "Mcp": {"mcpServers": "MCP Servers", "addMcpServer": "Add MCP Server", "name": "Name", "description": "Description", "status": "Status", "type": "Type", "action": "Action", "edit": "Edit", "delete": "Delete", "isEnable": "Enable", "enabled": "Enabled", "disabled": "Disabled", "sse": "SSE", "streamableHTTP": "Streamable HTTP", "tools": "Tools", "fieldRequired": "Required", "editMcpServer": "Edit MCP Server"}, "System": {"system": "System Settings", "isRegistrationOpen": "Open registration", "isRegistrationOpenDesc": "Users can apply for an account via email. Please do not enable for internal team use.", "saveFail": "Fail", "saveSuccess": "Success"}}}
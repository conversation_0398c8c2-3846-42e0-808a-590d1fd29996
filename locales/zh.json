{"Common": {"settings": "设置", "confirm": "确认", "cancel": "取消", "all": "全部", "edit": "编辑", "delete": "删除", "ok": "确定"}, "Auth": {"login": "登录", "emailNotice": "请输入 Email", "password": "密码", "repeatPassword": "重复密码", "passwordNotice": "请输入密码", "passwordError": "用户名或密码错误", "registerShot": "注册", "register": "注册账号", "hasAccount": "已有账号？", "clickToLogin": "点此登录", "passwordLengthLimit": "密码长度至少 8 个字符", "passwordNotSame": "两次密码不相同", "registerFail": "注册失败，请稍后再试", "openRegisterNotice": "未开放注册，请联系管理员", "adminCodeRequired": "请输入 Admin Code", "setupAdminAccount": "设置管理员账号", "adminCodeNotice": "根目录 .env 文件或环境变量 ADMIN_CODE 的值，用于首次安装设置管理员账号。", "setupNotice": "请设置管理员账号和密码，管理员账号设置完成后，此页面将无法访问。"}, "Onboarding": {"welcome": "欢迎使用 HiveChat", "subtitle": "创建您的第一个工作区，开始与 AI 助手对话", "workspaceName": "工作区名称", "workspaceNamePlaceholder": "例如：我的工作区", "workspaceNameRequired": "请输入工作区名称", "workspaceNameMinLength": "工作区名称至少需要 2 个字符", "workspaceNameMaxLength": "工作区名称不能超过 50 个字符", "workspaceId": "工作区 ID", "workspaceIdPlaceholder": "例如：my-workspace", "workspaceIdRequired": "请输入工作区 ID", "workspaceIdMinLength": "ID 至少需要 5 个字符", "workspaceIdMaxLength": "ID 不能超过 20 个字符", "workspaceIdPattern": "只能包含小写字母、数字和连字符", "workspaceIdHelp": "访问链接：https://cloud.hivechat.net/", "workspaceIdUnavailable": "不可用", "createWorkspace": "创建工作区", "createSuccess": "工作区创建成功！", "createFailed": "创建工作区失败", "createError": "创建工作区时发生错误", "idUnavailableError": "请选择一个可用的工作区 ID", "teamNotice": "创建工作区后，您可以邀请团队成员加入协作"}, "Chat": {"webSearch": "联网搜索", "mcpServer": "MCP 服务器", "mcpTool": "MCP 工具", "builtInImageGen": "图像生成", "mcpInput": "调用返回", "mcpOutput": "MCP 工具", "mcpFailed": "调用失败", "mcpFinished": "已完成", "mcpCall": "调用", "sTool": "的工具", "newChat": "新对话", "login": "登录", "defaultChatName": "新的聊天", "recentChat": "最近对话", "myBots": "我的智能体", "discoverBots": "发现 AI 智能体", "historyNotice": "对话记录将会出现在这里", "rename": "编辑", "delete": "删除", "viewAll": "查看全部", "editChatName": "编辑对话名称", "save": "保存", "confirm": "确定", "cancel": "取消", "chatNameplaceholder": "请输入对话名称", "settings": "设置", "noLlmNotice": "尚未设置任何大模型服务，", "clickToSet": "点此设置", "inputPlaceholder": "可以问我任何问题...", "notSupportVision": "当前模型不支持上传图片", "welcomeNotice": "有什么可以帮忙的？", "goodMorning": "早上好，", "goodAfternoon": "中午好，", "goodEvening": "下午好，", "goodNight": "晚上好，", "adminPanel": "管理后台", "historyChat": "历史对话", "today": "今天", "thisWeek": "本周", "thisMonth": "本月", "earlier": "更早之前", "renameChat": "编辑对话名称", "inputChatName": "请输入对话名称", "deleteCurrentChat": "删除当前对话", "deleteCurrentChatDetail": "对话所有记录都会被删除", "clearMemory": "清除记忆", "image": "图片", "unsupportImage": "当前模型不支持上传图片", "maxImageCount": "最多能上传 {maxImages} 张图片", "mustBeImage": "请上传图片文件", "imageSizeLimit": "图片大小不能超过 5MB", "clearHistoryMessage": "清空历史消息", "clearHistoryMessageShort": "清空消息", "confirmClearHistoryMessage": "确认要清除所有记录吗", "clearHistoryMessageNotice": "当前对话的所有记录将被删除", "startNewChat": "开启新对话", "piece": "条", "contextCleared": "上下文已清除", "contentParsingError": "内容解析错误", "deleteNotice": "删除提示", "currentMessageDelete": "当前消息将会被删除", "copy": "复制", "copySuccess": "复制成功", "retry": "重试", "unknownUsage": "未获取到 Token 消耗统计", "apiTimeout": "API 连接超时，请检查网络或 API 地址。", "apiKeyError": "API Key 不正确或为空，请联系管理员处理。", "historyMessageCount": "历史消息数", "historyMessageCountAllShot": "所有", "historyMessageCountAll": "所有消息", "historyMessageCountNone": "不记录", "historyMessageCountSpecify": "指定个数", "historyMessageCountAllNotice": "当前所有会话内容会作为上下文，Token 消耗会变多。", "historyMessageCountNoneNotice": "无上下文，每次对话都是新的开始，适用于翻译等临时性对话需求。", "historyMessageCountSpecifyNotice": "请指定要记录的消息数量：", "deleteSuccess": "删除成功", "deleteFail": "删除失败", "back": "返回", "notExist": "该智能体不存在，或已经被删除", "source": "来源", "chat": "对话", "addToChats": "添加到对话", "remove": "移除", "prompt": "角色设定（Prompt）", "noPrompt": "该智能体没有提示词", "createBot": "创建 AI 智能体", "botName": "名称", "botNameNotice": "输入名称", "botDesc": "描述", "botDescNotice": "简单介绍一下场景和使用方法", "promptNotice": "示例：你是一个经验丰富的语文老师，拥有激发学生学习热情的教学方法，你擅长运营幽默和实际案例，使教学充满趣味", "pin": "置顶", "unPin": "取消置顶", "modelNotConfigured": "模型未配置", "guideAlertText": "尚未配置大模型服务，AI 对话无法正常使用，请到管理后台设置后使用", "thinking": "思考中...", "thought": "已深度思考"}, "Settings": {"accountSettings": "账号设置", "systemSettings": "高级设置", "changePassword": "修改密码", "logout": "退出账号", "usage": "用量信息", "unlimitedTokens": "不限 Tokens", "usedThisMonth": "本月已用", "monthlyLimit": "本月上限", "nickname": "昵称", "workspace": "工作空间", "backToWorkspaceList": "返回工作空间列表", "deleteAllMessagesTitle": "删除所有对话消息", "deleteAllMessagesDesc": "所有对话消息都会被清空，清空后无法找回", "deleteAction": "删除所有对话", "deleteSuccess": "删除成功", "oldPassword": "旧密码", "newPassword": "新密码", "repeatNewPassword": "重复新密码", "inputPassword": "请输入密码", "lengthLimit": "长度不能小于 8 个字符", "language": "语言"}, "Admin": {"models": "模型设置", "defaultModel": "默认模型", "users": "用户管理", "webSearch": "搜索设置", "mcpServers": "MCP 服务器", "system": "系统设置", "backHome": "返回首页", "logout": "退出", "logoutNoticeTitle": "提示", "logoutNoticeDesc": "确认要退出吗？", "Models": {"modelSettings": "模型设置", "modelSettingsNotice": "← 请在左侧选择一个大模型供应商进行配置", "settings": "设置", "status": "启用", "enabled": "已启用", "disabled": "未启用", "endpoint": "中转地址", "serviceEndpoint": "服务地址", "configGuide": "设置指引", "save": "保存", "cancel": "取消", "testConnect": "连通性检查", "check": "检查", "checkSuccess": "检查通过", "checkFail": "检查失败", "optional": "选填", "models": "模型列表", "confirm": "确定", "hide": "隐藏", "addModel": "添加模型", "customModel": "自定义添加", "deleteCustomModel": "删除当前自定义模型", "currentModelWillbeDeleted": "当前模型将被删除", "deleteSuccess": "删除成功", "supportVision": "支持图像理解", "supportTool": "支持 MCP 工具调用", "conversationUpTo": "单个会话最多支持", "allAdded": "已全部添加", "confirmAndRetry": "确认并重试", "close": "关闭", "addCustomModel": "自定义添加模型", "modelId": "模型 ID", "modelDisplayName": "模型显示名称", "modelMaxToken": "最大 Token 数", "modelIdNotice": "模型 ID", "modelDisplayNameNotice": "模型显示名称", "modelMaxTokenNotice": "最大 Token 数", "okText": "保存", "cancelText": "取消", "addModelSuccess": "添加成功", "saveSuccess": "保存成功", "saveFailed": "保存成功", "selectModelToCheck": "选择要检测的模型", "checkConnectivity": "检查连通性"}, "Users": {"group": "分组", "userList": "用户列表", "addUser": "添加用户", "role": "角色", "emailNotice": "请输入邮箱", "password": "密码", "passwordNotice": "请输入密码", "lengthLimit": "长度不能小于 8 个字符", "roleAdmin": "管理员", "roleUser": "用户", "registerAt": "注册时间", "action": "操作", "edit": "编辑", "delete": "删除", "deleteNotice": "确定要删除此用户吗?", "deleteUserSuccess": "删除成功", "addUserSuccess": "添加成功", "updateUserSuccess": "更新成功", "editUser": "编辑用户", "groupManagement": "分组管理", "groupManagementTip": "使用用户分组，可以控制不同用户可以使用哪些模型，以及每月的 Token 用量。", "addGroup": "添加分组", "groupName": "分组名称", "availableModels": "可使用的模型", "specificModel": "指定模型", "deleteConfirmTitle": "删除提示", "deleteConfirmContent": "确认要删除该分组吗？\n\n删除后该分组下全部用户自动变更为「默认分组」", "defaultGroup": "默认分组", "defaultGroupCannotDelete": "默认分组无法删除", "deleteGroupSuccess": "删除成功"}, "Search": {"webSearch": "搜索设置", "enableWebSearch": "开启网络搜索", "enableWebSearchNotice": "开启后会先通过搜索引擎搜索相关内容，大模型将基于搜索结果进行回答。", "webSearchProvider": "搜索服务商", "apikey": "API 密钥", "getApikey": "获取密钥", "general": "常规设置", "searchResult": "搜索结果数", "defaultCount": "默认", "apikeyRequiredNotice": "请输入 API 密钥", "check": "检查"}, "Mcp": {"mcpServers": "MCP 服务器", "addMcpServer": "添加 MCP 服务器", "name": "名称", "description": "描述", "status": "状态", "type": "类型", "action": "操作", "edit": "编辑", "delete": "删除", "isEnable": "启用", "enabled": "已启用", "disabled": "未启用", "sse": "服务器发送事件(SSE)", "streamableHTTP": "可流式传输的 HTTP (Streamable HTTP)", "tools": "查看工具", "fieldRequired": "此项目为必填", "editMcpServer": "编辑 MCP 服务器"}, "System": {"system": "系统设置", "isRegistrationOpen": "开放注册", "isRegistrationOpenDesc": "用户可通过邮箱自助申请账号，内部团队使用请勿开启", "saveFail": "保存失败", "saveSuccess": "保存成功"}}}
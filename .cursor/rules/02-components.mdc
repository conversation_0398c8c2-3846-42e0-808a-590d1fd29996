---
description: 
globs: 
alwaysApply: false
---
# UI Components

The application uses a component-based architecture. Key components include:

## Chat Interface Components
- [app/components/App.tsx](mdc:app/components/App.tsx): Main application component
- [app/components/ChatList.tsx](mdc:app/components/ChatList.tsx): List of chat conversations
- [app/components/MessageList.tsx](mdc:app/components/MessageList.tsx): List of messages in a conversation
- [app/components/MessageItem.tsx](mdc:app/components/MessageItem.tsx): Individual message component
- [app/components/ResponsingMessage.tsx](mdc:app/components/ResponsingMessage.tsx): Component for messages being generated/responded to
- [app/components/AdaptiveTextarea.tsx](mdc:app/components/AdaptiveTextarea.tsx): Textarea that adapts to content

## UI Enhancement Components
- [app/components/Markdown.tsx](mdc:app/components/Markdown.tsx): Markdown rendering component
- [app/components/CodeBlock.tsx](mdc:app/components/CodeBlock.tsx): Code highlighting component
- [app/components/EmojiPicker.tsx](mdc:app/components/EmojiPicker.tsx): Emoji selection interface

## Navigation Components
- [app/components/Sidebar.tsx](mdc:app/components/Sidebar.tsx): Main sidebar navigation
- [app/components/SidebarMenuSection.tsx](mdc:app/components/SidebarMenuSection.tsx): Section within the sidebar

## Authentication Components
- [app/components/SignIn.tsx](mdc:app/components/SignIn.tsx): Sign-in component
- [app/components/loginModal.tsx](mdc:app/components/loginModal.tsx): Login modal dialog
- [app/components/WecomLoginButton.tsx](mdc:app/components/WecomLoginButton.tsx): Wecom login integration
- [app/components/DingdingLoginButton.tsx](mdc:app/components/DingdingLoginButton.tsx): Dingding login integration
- [app/components/FeishuLoginButton.tsx](mdc:app/components/FeishuLoginButton.tsx): Feishu login integration

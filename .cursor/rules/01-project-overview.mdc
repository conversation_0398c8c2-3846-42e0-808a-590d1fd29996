---
description: 
globs: 
alwaysApply: false
---
# HiveChat Project Overview

HiveChat is a chat application built with Next.js. The project follows a modern web application architecture with a clear separation of concerns.

## Key Files and Directories

- [app/layout.tsx](mdc:app/layout.tsx): The main layout component that wraps all pages in the application
- [app/page.tsx](mdc:app/page.tsx): The home page of the application
- [app/components](mdc:app/components): UI components used throughout the application
- [app/api](mdc:app/api): API routes for backend functionality
- [app/utils.ts](mdc:app/utils.ts): Utility functions used across the application
- [auth.ts](mdc:auth.ts): Authentication configuration

## Main Features

The application appears to be a chat interface with AI model integration, supporting:
- Multiple chat providers and models
- Authentication with various services (Wecom, Dingding, Feishu)
- Markdown rendering for messages
- Code block highlighting

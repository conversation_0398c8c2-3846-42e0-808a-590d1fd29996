---
description: 
globs: 
alwaysApply: false
---
# Authentication System

The application uses NextAuth.js for authentication, supporting multiple providers.

## Main Authentication Configuration
- [auth.ts](mdc:auth.ts): Core authentication configuration including providers and callbacks

## Authentication Providers
The application supports the following authentication methods:
- Email/Password (Credentials provider)
- Feishu authentication
- Wecom authentication
- Dingding authentication

## Authentication Components
The UI components related to authentication:
- [app/components/SignIn.tsx](mdc:app/components/SignIn.tsx): Sign-in component
- [app/components/loginModal.tsx](mdc:app/components/loginModal.tsx): Login modal dialog
- [app/components/WecomLoginButton.tsx](mdc:app/components/WecomLoginButton.tsx): Wecom login integration
- [app/components/DingdingLoginButton.tsx](mdc:app/components/DingdingLoginButton.tsx): Dingding login integration
- [app/components/FeishuLoginButton.tsx](mdc:app/components/FeishuLoginButton.tsx): Feishu login integration

## Authentication Flow
The authentication flow includes:
1. User sign-in through one of the supported providers
2. JWT token generation with user information
3. Session creation with user details including admin status

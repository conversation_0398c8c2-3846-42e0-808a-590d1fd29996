# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `npm run dev` - Start development server on http://localhost:3000
- `npm run build` - Build production application  
- `npm start` - Start production server on port 3000
- `npm run lint` - Run ESLint for code quality checks
- `npm run initdb` - Initialize/update database schema (run after schema changes)

## Architecture Overview

HiveChat is a Next.js-based AI chat application with workspace management, supporting multiple AI providers and authentication methods.

### Core Structure
- **Next.js App Router** - Uses app directory structure with nested layouts
- **Database**: PostgreSQL with Dr<PERSON>zle ORM for schema management
- **Authentication**: NextAuth.js with multiple providers (email, Feishu, Wecom, Dingding)
- **State Management**: Zustand for client-side state
- **UI Framework**: Ant Design with Tailwind CSS
- **AI Integration**: Streaming support for OpenAI, Claude, Gemini, DeepSeek and others

### Key Directories
- `app/[workspace]/` - Workspace-scoped pages with admin and chat functionality
- `app/api/[workspace]/` - Workspace API routes for completions and billing
- `app/components/` - Reusable UI components
- `app/db/` - Database schema, relations, and seed data
- `app/provider/` - AI provider implementations for different models
- `app/store/` - Zustand stores for global state management

### Database Operations
- Schema defined in `app/db/schema.ts` with relations in `app/db/relations.ts`
- Migration files in `drizzle/migrations/`
- Use `npm run initdb` after schema changes to update database

### AI Provider Integration
- Each provider has dedicated proxy handlers in `app/api/[workspace]/completions/`
- Streaming implementations in `app/provider/` for real-time responses
- Model configurations in `app/db/data/models/`

### Authentication Flow
- Multi-provider setup in `auth.ts` with JWT token handling
- Workspace-based access control with admin privileges
- Setup page at `/setup` for initial admin account creation

### Key Features
- Multi-workspace support with role-based access
- Credit system for usage tracking and billing
- MCP (Model Context Protocol) server integration
- Web search integration (Tavily, Jina, Bocha)
- Bot/Agent creation and management
- Stripe integration for payments
- Docker deployment support

## Environment Setup

Required environment variables (see `.env.example`):
- `DATABASE_URL` - PostgreSQL connection string
- `AUTH_SECRET` - JWT signing secret
- `ADMIN_CODE` - Initial admin setup code
- `NEXTAUTH_URL` - Application URL for auth callbacks
- Provider-specific keys for AI models and authentication services
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "隐私政策 - HiveChat",
  description: "HiveChat 多租户 AI 聊天机器人平台隐私政策，了解我们如何收集、使用、保护您的个人信息和数据安全措施。",
  keywords: "HiveChat, 隐私政策, 数据保护, AI聊天机器人, 多租户, SaaS, 个人信息保护",
  robots: "index, follow",
  openGraph: {
    title: "隐私政策 - HiveChat",
    description: "HiveChat 多租户 AI 聊天机器人平台隐私政策",
    type: "website",
  },
};

export default function PrivacyLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}

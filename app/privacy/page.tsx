"use client";
import React from 'react';
import { Typography, Card, Space } from 'antd';
import { SecurityScanOutlined, SafetyOutlined, TeamOutlined, CloudOutlined, LockOutlined, EyeOutlined, DatabaseOutlined, GlobalOutlined } from '@ant-design/icons';
import Header from '@/app/components/Header';

const { Title, Text, Paragraph } = Typography;

export default function PrivacyPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <Header />

      {/* Main Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 max-w-4xl">
        {/* Page Header */}
        <div className="text-center mb-8">
          <Title level={1} className="!text-3xl !font-bold !text-gray-900 !mb-4">
            <SecurityScanOutlined className="mr-3 text-green-600" />
            隐私政策
          </Title>
          <Text type="secondary" className="text-lg">
            HiveChat 平台隐私政策
          </Text>
          <div className="mt-4">
            <Text type="secondary" className="text-sm">
              最后更新时间：2025年7月
            </Text>
          </div>
        </div>

        {/* Privacy Content */}
        <Space direction="vertical" size="large" className="w-full">

          {/* 引言 */}
          <Card className="shadow-sm bg-blue-50 border-blue-200">
            <Paragraph className="text-blue-800 !mb-2">
              <strong>重要提示</strong>
            </Paragraph>
            <Paragraph className="text-blue-700 !mb-0">
              HiveChat 深知个人信息对您的重要性，并会尽全力保护您的个人信息安全可靠。我们致力于维持您对我们的信任，恪守以下原则，保护您的个人信息：权责一致原则、目的明确原则、选择同意原则、最少够用原则、确保安全原则、主体参与原则、公开透明原则等。同时，我们承诺，我们将按业界成熟的安全标准，采取相应的安全保护措施来保护您的个人信息。
            </Paragraph>
          </Card>

          {/* 1. 数据收集类型和目的 */}
          <Card id="data-collection" className="shadow-sm">
            <Title level={2} className="!text-xl !font-semibold !text-gray-800 !mb-4">
              <DatabaseOutlined className="mr-2 text-blue-600" />
              1. 数据收集类型和目的
            </Title>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>1.1 个人身份信息</strong><br />
              • <strong>收集内容</strong>：邮箱地址、用户名、头像（如通过第三方登录提供）<br />
              • <strong>收集目的</strong>：用户身份验证、账户管理、服务提供<br />
              • <strong>法律依据</strong>：履行合同必要、合法权益
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>1.2 工作空间信息</strong><br />
              • <strong>收集内容</strong>：工作空间名称、工作空间 ID、成员列表、角色权限<br />
              • <strong>收集目的</strong>：多租户服务提供、团队协作功能、权限管理<br />
              • <strong>法律依据</strong>：履行合同必要
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>1.3 对话数据</strong><br />
              • <strong>收集内容</strong>：用户输入的对话内容、AI 生成的回复、对话历史记录<br />
              • <strong>收集目的</strong>：提供 AI 对话服务、保存用户对话历史、服务优化<br />
              • <strong>法律依据</strong>：履行合同必要、用户同意
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>1.4 技术信息</strong><br />
              • <strong>收集内容</strong>：IP 地址、设备信息、浏览器类型、操作系统、访问时间<br />
              • <strong>收集目的</strong>：系统安全、故障排查、服务优化、用户体验改进<br />
              • <strong>法律依据</strong>：合法权益、技术必要性
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>1.5 使用统计</strong><br />
              • <strong>收集内容</strong>：功能使用频率、API 调用次数、错误日志<br />
              • <strong>收集目的</strong>：服务质量监控、性能优化、产品改进<br />
              • <strong>法律依据</strong>：合法权益、技术必要性
            </Paragraph>
          </Card>

          {/* 2. 用户个人信息处理方式 */}
          <Card id="data-processing" className="shadow-sm">
            <Title level={2} className="!text-xl !font-semibold !text-gray-800 !mb-4">
              <SafetyOutlined className="mr-2 text-green-600" />
              2. 用户个人信息处理方式
            </Title>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>2.1 处理原则</strong><br />
              • <strong>最小化原则</strong>：仅收集和处理提供服务所必需的个人信息<br />
              • <strong>目的限制</strong>：仅在明确告知的目的范围内处理个人信息<br />
              • <strong>准确性保证</strong>：确保个人信息的准确性和时效性<br />
              • <strong>存储限制</strong>：仅在必要期限内保存个人信息
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>2.2 处理活动</strong><br />
              • <strong>收集</strong>：通过注册、登录、使用服务等方式收集<br />
              • <strong>存储</strong>：在安全的云服务器中加密存储<br />
              • <strong>使用</strong>：用于提供服务、改进产品、技术支持<br />
              • <strong>共享</strong>：仅在法律要求或用户同意的情况下共享<br />
              • <strong>删除</strong>：在保存期限届满或用户要求时安全删除
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>2.3 自动化处理</strong><br />
              • 我们可能使用自动化技术处理您的个人信息以提供个性化服务<br />
              • 您有权要求人工审核自动化决策结果<br />
              • 重要决策不会完全基于自动化处理
            </Paragraph>
          </Card>

          {/* 3. 对话数据的存储和使用 */}
          <Card id="conversation-data" className="shadow-sm">
            <Title level={2} className="!text-xl !font-semibold !text-gray-800 !mb-4">
              <LockOutlined className="mr-2 text-purple-600" />
              3. 对话数据的存储和使用
            </Title>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>3.1 存储方式</strong><br />
              • <strong>加密存储</strong>：所有对话数据采用 AES-256 加密算法存储<br />
              • <strong>访问控制</strong>：严格的权限控制，仅授权人员可访问<br />
              • <strong>备份机制</strong>：定期备份，确保数据安全和可恢复性<br />
              • <strong>地理位置</strong>：数据存储在中国境内的合规数据中心
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>3.2 使用目的</strong><br />
              • <strong>服务提供</strong>：为您提供 AI 对话服务和历史记录查看<br />
              • <strong>质量改进</strong>：分析对话模式以改进服务质量（仅统计分析，不涉及具体内容）<br />
              • <strong>技术支持</strong>：在您明确同意的情况下，用于问题排查和技术支持<br />
              • <strong>安全监控</strong>：检测和防范恶意使用、滥用行为
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>3.3 保存期限</strong><br />
              • <strong>活跃用户</strong>：对话数据在您的账户活跃期间保存<br />
              • <strong>非活跃用户</strong>：账户非活跃超过 1 年后，对话数据将被删除<br />
              • <strong>账户删除</strong>：您删除账户后，对话数据将在 30 天内完全删除<br />
              • <strong>法律要求</strong>：在法律法规要求的期限内保存相关数据
            </Paragraph>
          </Card>

          {/* 4. API Key 等敏感信息的保护 */}
          <Card id="api-key-protection" className="shadow-sm">
            <Title level={2} className="!text-xl !font-semibold !text-gray-800 !mb-4">
              <LockOutlined className="mr-2 text-red-600" />
              4. API Key 等敏感信息的保护
            </Title>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>4.1 加密保护</strong><br />
              • <strong>传输加密</strong>：所有 API Key 传输过程使用 HTTPS/TLS 1.3 加密<br />
              • <strong>存储加密</strong>：API Key 使用 AES-256 加密算法存储，密钥独立管理<br />
              • <strong>内存保护</strong>：运行时 API Key 在内存中加密处理，使用后立即清除<br />
              • <strong>访问日志</strong>：记录所有 API Key 访问日志，便于安全审计
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>4.2 访问控制</strong><br />
              • <strong>最小权限</strong>：仅工作空间授权成员可查看和管理 API Key<br />
              • <strong>角色分离</strong>：不同角色拥有不同的 API Key 管理权限<br />
              • <strong>操作审计</strong>：记录所有 API Key 相关操作，包括添加、修改、删除<br />
              • <strong>异常监控</strong>：监控异常访问行为，及时发现安全威胁
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>4.3 使用规范</strong><br />
              • <strong>用途限制</strong>：API Key 仅用于为您提供 AI 服务，不用于其他目的<br />
              • <strong>不会共享</strong>：我们绝不会向第三方共享您的 API Key<br />
              • <strong>定期轮换</strong>：建议您定期更换 API Key 以提高安全性<br />
              • <strong>泄露处理</strong>：如发现 API Key 泄露，我们将立即通知您并协助处理
            </Paragraph>
          </Card>

          {/* 5. 第三方 AI 服务商数据共享政策 */}
          <Card id="third-party-sharing" className="shadow-sm">
            <Title level={2} className="!text-xl !font-semibold !text-gray-800 !mb-4">
              <CloudOutlined className="mr-2 text-orange-600" />
              5. 第三方 AI 服务商数据共享政策
            </Title>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>5.1 数据共享范围</strong><br />
              • <strong>OpenAI</strong>：用户对话内容将发送至 OpenAI 服务器进行处理<br />
              • <strong>Anthropic</strong>：用户对话内容将发送至 Anthropic 服务器进行处理<br />
              • <strong>Google</strong>：用户对话内容将发送至 Google Gemini 服务器进行处理<br />
              • <strong>DeepSeek</strong>：用户对话内容将发送至 DeepSeek 服务器进行处理<br />
              • <strong>其他服务商</strong>：根据用户选择的 AI 模型，对话内容将发送至相应服务商
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>5.2 共享目的</strong><br />
              • <strong>服务提供</strong>：为您提供 AI 对话和内容生成服务<br />
              • <strong>模型推理</strong>：第三方服务商处理您的输入并生成回复<br />
              • <strong>服务优化</strong>：部分服务商可能使用数据改进其模型（具体政策请查看各服务商隐私政策）
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>5.3 数据保护措施</strong><br />
              • <strong>传输安全</strong>：所有数据传输均使用 HTTPS 加密<br />
              • <strong>最小化传输</strong>：仅传输必要的对话内容，不传输其他个人信息<br />
              • <strong>服务商选择</strong>：我们仅与具有良好隐私保护记录的服务商合作<br />
              • <strong>用户控制</strong>：您可以选择使用哪些 AI 服务商的模型
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>5.4 重要提醒</strong><br />
              • 各 AI 服务商有其独立的隐私政策和数据处理规则<br />
              • 建议您查阅相关服务商的隐私政策了解详细信息<br />
              • 如您不同意数据传输给某个服务商，可选择不使用该服务商的模型<br />
              • 我们会持续监控服务商的隐私保护措施
            </Paragraph>
          </Card>

          {/* 6. 工作空间数据隔离和多租户隐私保护 */}
          <Card id="workspace-isolation" className="shadow-sm">
            <Title level={2} className="!text-xl !font-semibold !text-gray-800 !mb-4">
              <TeamOutlined className="mr-2 text-cyan-600" />
              6. 工作空间数据隔离和多租户隐私保护
            </Title>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>6.1 数据隔离机制</strong><br />
              • <strong>物理隔离</strong>：不同工作空间的数据在数据库层面完全隔离<br />
              • <strong>逻辑隔离</strong>：应用层面严格的权限控制，确保数据访问隔离<br />
              • <strong>网络隔离</strong>：不同工作空间的网络请求独立处理<br />
              • <strong>存储隔离</strong>：文件和对话记录按工作空间独立存储
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>6.2 权限控制</strong><br />
              • <strong>基于角色</strong>：不同角色（所有者、管理员、成员）拥有不同权限<br />
              • <strong>最小权限</strong>：用户仅能访问其有权限的工作空间数据<br />
              • <strong>动态权限</strong>：权限变更实时生效，离开工作空间后立即失去访问权限<br />
              • <strong>审计日志</strong>：记录所有数据访问和权限变更操作
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>6.3 多租户安全</strong><br />
              • <strong>租户识别</strong>：每个请求都包含明确的工作空间标识<br />
              • <strong>数据验证</strong>：严格验证用户对数据的访问权限<br />
              • <strong>跨租户防护</strong>：防止跨工作空间的数据泄露<br />
              • <strong>安全监控</strong>：实时监控异常访问行为
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>6.4 团队协作隐私</strong><br />
              • <strong>成员可见性</strong>：工作空间成员可以看到共享的对话记录<br />
              • <strong>私有对话</strong>：支持创建私有对话，仅创建者可见<br />
              • <strong>权限透明</strong>：清楚显示谁可以访问哪些数据<br />
              • <strong>退出保护</strong>：成员离开工作空间后无法访问相关数据
            </Paragraph>
          </Card>

          {/* 7. 用户权利 */}
          <Card id="user-rights" className="shadow-sm">
            <Title level={2} className="!text-xl !font-semibold !text-gray-800 !mb-4">
              <EyeOutlined className="mr-2 text-indigo-600" />
              7. 用户权利（访问、修改、删除数据等）
            </Title>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>7.1 知情权</strong><br />
              • <strong>数据类型</strong>：您有权了解我们收集了哪些个人信息<br />
              • <strong>使用目的</strong>：您有权了解个人信息的使用目的和方式<br />
              • <strong>共享情况</strong>：您有权了解个人信息的共享情况<br />
              • <strong>存储期限</strong>：您有权了解个人信息的存储期限
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>7.2 访问权</strong><br />
              • <strong>数据查看</strong>：您可以随时查看自己的个人信息和对话记录<br />
              • <strong>数据导出</strong>：您可以申请导出自己的个人数据<br />
              • <strong>处理记录</strong>：您可以了解个人信息的处理记录<br />
              • <strong>响应时间</strong>：我们将在 15 个工作日内响应您的访问请求
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>7.3 更正权</strong><br />
              • <strong>信息更正</strong>：您可以更正不准确或不完整的个人信息<br />
              • <strong>实时更新</strong>：大部分信息可以通过账户设置实时更新<br />
              • <strong>申请更正</strong>：对于无法自行更正的信息，可以申请我们协助更正<br />
              • <strong>更正确认</strong>：更正完成后我们会及时通知您
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>7.4 删除权</strong><br />
              • <strong>账户删除</strong>：您可以随时删除自己的账户<br />
              • <strong>数据删除</strong>：账户删除后，相关个人数据将在 30 天内删除<br />
              • <strong>选择性删除</strong>：您可以删除特定的对话记录<br />
              • <strong>法律保留</strong>：法律要求保留的数据将在法定期限后删除
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>7.5 撤回同意权</strong><br />
              • <strong>同意撤回</strong>：对于基于同意处理的个人信息，您可以随时撤回同意<br />
              • <strong>撤回方式</strong>：通过账户设置或联系客服撤回同意<br />
              • <strong>撤回后果</strong>：撤回同意可能影响相关服务的提供<br />
              • <strong>历史数据</strong>：撤回同意不影响撤回前基于同意的处理活动
            </Paragraph>
          </Card>

          {/* 8. Cookie 和追踪技术使用 */}
          <Card id="cookies-tracking" className="shadow-sm">
            <Title level={2} className="!text-xl !font-semibold !text-gray-800 !mb-4">
              <GlobalOutlined className="mr-2 text-yellow-600" />
              8. Cookie 和追踪技术使用
            </Title>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>8.1 Cookie 类型</strong><br />
              • <strong>必要 Cookie</strong>：维持用户登录状态、会话管理等基本功能<br />
              • <strong>功能 Cookie</strong>：记住用户偏好设置、语言选择等<br />
              • <strong>分析 Cookie</strong>：收集网站使用统计信息，帮助改进服务<br />
              • <strong>第三方 Cookie</strong>：来自第三方服务（如认证服务）的 Cookie
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>8.2 使用目的</strong><br />
              • <strong>身份验证</strong>：维持用户登录状态和会话安全<br />
              • <strong>个性化</strong>：记住用户的偏好设置和配置<br />
              • <strong>安全保护</strong>：防范恶意攻击和异常行为<br />
              • <strong>服务优化</strong>：分析用户行为以改进产品体验
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>8.3 用户控制</strong><br />
              • <strong>浏览器设置</strong>：您可以通过浏览器设置管理 Cookie<br />
              • <strong>选择性接受</strong>：您可以选择接受或拒绝非必要 Cookie<br />
              • <strong>清除 Cookie</strong>：您可以随时清除已存储的 Cookie<br />
              • <strong>影响说明</strong>：禁用某些 Cookie 可能影响网站功能
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>8.4 其他追踪技术</strong><br />
              • <strong>本地存储</strong>：使用浏览器本地存储保存用户偏好<br />
              • <strong>会话存储</strong>：临时存储会话相关信息<br />
              • <strong>设备指纹</strong>：不使用设备指纹技术进行用户追踪<br />
              • <strong>第三方追踪</strong>：不使用第三方追踪服务
            </Paragraph>
          </Card>

          {/* 9. 数据安全措施 */}
          <Card id="data-security" className="shadow-sm">
            <Title level={2} className="!text-xl !font-semibold !text-gray-800 !mb-4">
              <LockOutlined className="mr-2 text-red-600" />
              9. 数据安全措施
            </Title>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>9.1 技术安全措施</strong><br />
              • <strong>加密传输</strong>：所有数据传输使用 HTTPS/TLS 1.3 加密<br />
              • <strong>加密存储</strong>：敏感数据使用 AES-256 加密算法存储<br />
              • <strong>访问控制</strong>：基于角色的访问控制和最小权限原则<br />
              • <strong>防火墙保护</strong>：多层防火墙和入侵检测系统<br />
              • <strong>安全审计</strong>：定期进行安全漏洞扫描和渗透测试
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>9.2 管理安全措施</strong><br />
              • <strong>员工培训</strong>：定期进行数据安全和隐私保护培训<br />
              • <strong>权限管理</strong>：严格控制员工对用户数据的访问权限<br />
              • <strong>保密协议</strong>：所有员工签署严格的保密协议<br />
              • <strong>安全制度</strong>：建立完善的信息安全管理制度<br />
              • <strong>应急响应</strong>：制定数据安全事件应急响应预案
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>9.3 物理安全措施</strong><br />
              • <strong>数据中心</strong>：使用符合国际标准的安全数据中心<br />
              • <strong>环境控制</strong>：温度、湿度、电力等环境监控<br />
              • <strong>访问控制</strong>：严格的物理访问控制和监控<br />
              • <strong>设备安全</strong>：服务器设备的安全配置和管理<br />
              • <strong>备份恢复</strong>：多地备份和灾难恢复机制
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>9.4 安全事件处理</strong><br />
              • <strong>监控预警</strong>：7×24 小时安全监控和预警<br />
              • <strong>事件响应</strong>：快速响应和处理安全事件<br />
              • <strong>用户通知</strong>：及时通知受影响的用户<br />
              • <strong>改进措施</strong>：根据安全事件持续改进安全措施<br />
              • <strong>监管报告</strong>：按法律要求向监管部门报告重大安全事件
            </Paragraph>
          </Card>

          {/* 10. 跨境数据传输 */}
          <Card id="cross-border-transfer" className="shadow-sm">
            <Title level={2} className="!text-xl !font-semibold !text-gray-800 !mb-4">
              <GlobalOutlined className="mr-2 text-blue-600" />
              10. 跨境数据传输
            </Title>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>10.1 传输场景</strong><br />
              • <strong>AI 服务调用</strong>：调用海外 AI 服务商（如 OpenAI、Anthropic）时需要跨境传输对话数据<br />
              • <strong>云服务使用</strong>：使用海外云服务提供商的服务时可能涉及数据跨境<br />
              • <strong>技术支持</strong>：在获得您明确同意的情况下，为解决技术问题可能需要跨境传输<br />
              • <strong>法律要求</strong>：应法律法规要求或司法机关要求进行的数据传输
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>10.2 传输原则</strong><br />
              • <strong>必要性原则</strong>：仅在必要时进行跨境数据传输<br />
              • <strong>最小化原则</strong>：仅传输必要的数据，不传输无关信息<br />
              • <strong>安全保障</strong>：确保跨境传输过程中的数据安全<br />
              • <strong>合规要求</strong>：遵守中国和目标国家的相关法律法规
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>10.3 安全措施</strong><br />
              • <strong>加密传输</strong>：所有跨境数据传输均使用强加密技术<br />
              • <strong>合作伙伴审查</strong>：严格审查海外合作伙伴的数据保护能力<br />
              • <strong>合同约束</strong>：与海外服务商签署严格的数据保护协议<br />
              • <strong>监控审计</strong>：对跨境数据传输进行监控和审计
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>10.4 用户权利</strong><br />
              • <strong>知情权</strong>：我们会明确告知您数据跨境传输的情况<br />
              • <strong>选择权</strong>：您可以选择不使用涉及跨境传输的服务<br />
              • <strong>撤回权</strong>：您可以随时撤回对跨境传输的同意<br />
              • <strong>救济权</strong>：如因跨境传输造成损失，您有权要求救济
            </Paragraph>
          </Card>

          {/* 11. 隐私政策变更通知 */}
          <Card id="policy-changes" className="shadow-sm">
            <Title level={2} className="!text-xl !font-semibold !text-gray-800 !mb-4">
              <EyeOutlined className="mr-2 text-green-600" />
              11. 隐私政策变更通知
            </Title>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>11.1 变更原则</strong><br />
              • <strong>必要性</strong>：仅在必要时更新隐私政策<br />
              • <strong>透明性</strong>：清楚说明变更的内容和原因<br />
              • <strong>及时性</strong>：及时通知用户政策变更<br />
              • <strong>合理性</strong>：确保变更内容合理且符合法律要求
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>11.2 通知方式</strong><br />
              • <strong>网站公告</strong>：在网站显著位置发布变更公告<br />
              • <strong>邮件通知</strong>：向注册用户发送邮件通知<br />
              • <strong>应用内通知</strong>：通过应用内消息推送通知<br />
              • <strong>弹窗提醒</strong>：重要变更通过弹窗形式提醒用户
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>11.3 通知时间</strong><br />
              • <strong>重大变更</strong>：提前 30 天通知用户<br />
              • <strong>一般变更</strong>：提前 15 天通知用户<br />
              • <strong>紧急变更</strong>：因法律要求或安全需要的紧急变更将立即通知<br />
              • <strong>生效时间</strong>：明确告知新政策的生效时间
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>11.4 用户选择</strong><br />
              • <strong>继续使用</strong>：继续使用服务视为接受新的隐私政策<br />
              • <strong>停止使用</strong>：如不同意变更，可以停止使用服务<br />
              • <strong>数据处理</strong>：停止使用后，我们将按照原政策处理您的数据<br />
              • <strong>意见反馈</strong>：欢迎您对政策变更提出意见和建议
            </Paragraph>
          </Card>

          {/* 联系信息 */}
          <Card className="shadow-sm bg-green-50 border-green-200">
            <Title level={3} className="!text-lg !font-semibold !text-green-800 !mb-3">
              隐私问题联系我们
            </Title>
            <Paragraph className="text-green-700 !mb-2">
              如果您对本隐私政策有任何疑问、意见或建议，或需要行使您的个人信息权利，请通过以下方式联系我们：
            </Paragraph>
            <Paragraph className="text-green-700 !mb-0">
              • 邮箱：<EMAIL><br />
              • 工作时间：周一至周五 9:00-18:00<br />
              • 响应时间：我们将在 15 个工作日内回复您的咨询
            </Paragraph>
          </Card>

          {/* 法律声明 */}
          <Card className="shadow-sm bg-gray-50 border-gray-200">
            <Paragraph className="text-gray-600 text-sm !mb-0 text-center">
              我们承诺严格遵守相关法律法规，保护您的个人信息安全。
              <br /><br />
              <strong>HiveChat 团队</strong>
            </Paragraph>
          </Card>

        </Space>
      </div>
    </div>
  );
}
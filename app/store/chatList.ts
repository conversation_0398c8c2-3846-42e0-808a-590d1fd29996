import { create } from 'zustand';
import { updateChatTitleInServer, updateChatInServer } from '@/app/[workspace]/chat/actions/chat';
import { addBotToChatInServer } from '@/app/[workspace]/chat/actions/bot';
import { ChatType } from '@/types/llm';

interface IChatListStore {
  chatList: ChatType[];
  setNewTitle: (workspaceId: string, chatId: string, newTitle: string) => void;
  setChatList: (chatList: ChatType[]) => void;
  updateChat: (workspaceId: string, chatId: string, chat: {
    title?: string;
    defaultModel?: string;
    historyType?: 'all' | 'none' | 'count';
    historyCount?: number;
    isStar?: boolean;
    isWithBot?: boolean;
    botId?: number;
    avatar?: string;
    avatarType?: 'emoji' | 'url' | 'none';
    prompt?: string;
    starAt?: Date;
  }) => void;
  addBot: (workspaceId: string, botId: number) => void;
}

const useChatListStore = create<IChatListStore>((set) => ({
  chatList: [],
  setNewTitle: (workspaceId: string, chatId: string, newTitle: string) => {
    set((state) => {
      updateChatTitleInServer(workspaceId, chatId, newTitle);
      // 同步更新聊天列表
      const chatList = state.chatList.map(chat => {
        if (chat.id === chatId) {
          return { ...chat, title: newTitle };
        }
        return chat;
      });
      return { chatList };
    });
  },
  updateChat: (workspaceId: string, chatId: string, newChatInfo) => {
    set((state) => {
      updateChatInServer(workspaceId, chatId, newChatInfo);
      const chatList = state.chatList.map(chat => {
        if (chat.id === chatId) {
          return { ...chat, ...newChatInfo };
        }
        return chat;
      });
      return { chatList };
    });
  },
  setChatList: (chatList: ChatType[]) => {
    set(() => {
      return { chatList: chatList };
    });
  },

  addBot: async (workspaceId: string, botId: number) => {
    const result = await addBotToChatInServer(workspaceId, botId);
    set((state) => ({
      chatList: [result.data as ChatType, ...state.chatList],
    }));
  },

}))

export default useChatListStore

"use client";

import { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { getWorkspaceInfo } from '@/app/actions/workspace';

// 简单的内存缓存，避免重复请求
const adminStatusCache = new Map<string, { isAdmin: boolean; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

interface UseWorkspaceAdminResult {
  isAdmin: boolean;
  isLoading: boolean;
  error: string | null;
}

/**
 * 检查当前用户是否为指定工作空间的管理员（owner 或 admin）
 * @param workspaceId - 工作空间ID
 */
export function useWorkspaceAdmin(workspaceId: string | null): UseWorkspaceAdminResult {
  const { data: session, status } = useSession();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  useEffect(() => {
    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    async function checkAdminStatus() {
      // 如果没有session或workspaceId，直接返回
      if (status === 'loading') {
        return;
      }

      if (!session?.user?.id || !workspaceId) {
        setIsAdmin(false);
        setIsLoading(false);
        setError(null);
        return;
      }

      // 检查缓存
      const cacheKey = `${session.user.id}-${workspaceId}`;
      const cached = adminStatusCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        setIsAdmin(cached.isAdmin);
        setIsLoading(false);
        setError(null);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // 创建新的 AbortController
        abortControllerRef.current = new AbortController();

        // 获取用户在当前工作空间的角色
        const result = await getWorkspaceInfo(workspaceId);

        // 检查请求是否被取消
        if (abortControllerRef.current?.signal.aborted) {
          return;
        }

        if (result.status === 'success' && result.data) {
          // 检查角色是否为 owner 或 admin
          const userRole = result.data.role;
          const isAdminResult = userRole === 'owner' || userRole === 'admin';

          // 更新缓存
          adminStatusCache.set(cacheKey, {
            isAdmin: isAdminResult,
            timestamp: Date.now()
          });

          setIsAdmin(isAdminResult);
        } else {
          setIsAdmin(false);
          setError(result.message || 'Failed to get workspace role');
        }
      } catch (err) {
        // 如果是取消请求，不更新状态
        if (abortControllerRef.current?.signal.aborted) {
          return;
        }
        console.error('Error checking workspace admin status:', err);
        setIsAdmin(false);
        setError('Failed to check admin status');
      } finally {
        if (!abortControllerRef.current?.signal.aborted) {
          setIsLoading(false);
        }
      }
    }

    checkAdminStatus();

    // 清理函数
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [session, status, workspaceId]);

  return {
    isAdmin,
    isLoading,
    error
  };
}

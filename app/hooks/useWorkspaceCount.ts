'use client';

import { useState, useEffect } from 'react';
import { getUserWorkspaces } from '@/app/actions/workspace';

interface UseWorkspaceCountResult {
  workspaceCount: number;
  isLoading: boolean;
  error: string | null;
  shouldShowSelector: boolean;
  refetch: () => void;
}

/**
 * 自定义 Hook 用于获取用户的 workspace 数量
 * 并判断是否应该显示 WorkspaceSelector 组件
 */
export const useWorkspaceCount = (): UseWorkspaceCountResult => {
  const [workspaceCount, setWorkspaceCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchWorkspaceCount = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await getUserWorkspaces();
      
      if (result.status === 'success') {
        setWorkspaceCount(result.data.length);
      } else {
        setError(result.message || '获取工作空间数量失败');
        setWorkspaceCount(0);
      }
    } catch (err) {
      setError('获取工作空间数量时发生错误');
      setWorkspaceCount(0);
      console.error('Error fetching workspace count:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchWorkspaceCount();
  }, []);

  return {
    workspaceCount,
    isLoading,
    error,
    shouldShowSelector: workspaceCount >= 2,
    refetch: fetchWorkspaceCount,
  };
};

'use server';
import bcrypt from "bcryptjs";
import { eq } from 'drizzle-orm';
import { users } from '@/app/db/schema';
import { db } from '@/app/db';
import { signIn } from '@/auth';
import { linkUserToPendingWorkspaces } from '@/app/[workspace]/admin/users/list/actions';

export async function register(email: string, password: string, callbackUrl?: string) {
  try {
    const user = await db.query.users
      .findFirst({
        where: eq(users.email, email)
      })
    if (user) {
      return {
        status: 'fail',
        message: '该邮箱已被注册',
      };
    }
    // 生成盐值 (salt)，指定盐值的回合次数（通常是 10）
    const salt = await bcrypt.genSalt(10);

    // 使用盐值对密码进行哈希处理
    const hashedPassword = await bcrypt.hash(password, salt);
    // 将新用户数据插入数据库
    const [newUser] = await db.insert(users).values({
      email,
      password: hashedPassword,
    }).returning();

    // 自动关联到待定的workspace记录
    if (newUser) {
      await linkUserToPendingWorkspaces(newUser.id, email);
    }

    // 注册成功后，自动登录
    const signInResponse = await signIn("credentials", {
      redirect: false, // 不跳转页面
      email,
      password,
      callbackUrl: callbackUrl || '/',
    });
    // 返回成功消息或其他所需数据
    return {
      status: 'success',
    }
  } catch (error) {
    console.log(error)
    throw new Error('用户注册失败，请稍后再试');
  }
}

export async function loginWithCredentials(email: string, password: string, callbackUrl?: string) {
  try {
    const signInResponse = await signIn("credentials", {
      redirect: false, // 不跳转页面
      email,
      password,
      callbackUrl: callbackUrl || '/',
    });

    if (signInResponse?.error) {
      return {
        status: 'fail',
        message: '邮箱或密码错误',
      };
    }

    return {
      status: 'success',
      url: signInResponse?.url,
    };
  } catch (error) {
    console.log(error);
    return {
      status: 'fail',
      message: '登录失败，请稍后再试',
    };
  }
}


'use client';

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Typography, 
  Table, 
  Tag, 
  Space, 
  Pagination, 
  message,
  Modal,
  Descriptions,
  Button
} from 'antd';
import { 
  ShopOutlined, 
  EyeOutlined,
  CreditCardOutlined,
  TeamOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { getWorkspaceList, getWorkspaceDetails } from './actions';

const { Title, Paragraph } = Typography;

interface WorkspaceInfo {
  id: string;
  name: string;
  ownerEmail: string | null;
  totalCredits: number;
  memberCount: number;
  createdAt: Date | null;
  plan: string;
}

export default function WorkspacesManagementPage() {
  const [workspaces, setWorkspaces] = useState<WorkspaceInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [pageSize] = useState(20);
  const [selectedWorkspace, setSelectedWorkspace] = useState<any>(null);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);

  const loadWorkspaces = async (page: number = currentPage) => {
    try {
      setLoading(true);
      const result = await getWorkspaceList({
        page,
        limit: pageSize
      });
      
      setWorkspaces(result.workspaces);
      setTotal(result.total);
      setCurrentPage(page);
    } catch (error: any) {
      message.error(error.message || '加载工作空间列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadWorkspaces(1);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadWorkspaces(page);
  };

  const handleViewDetails = async (workspaceId: string) => {
    try {
      setLoading(true);
      const workspaceDetails = await getWorkspaceDetails(workspaceId);
      setSelectedWorkspace(workspaceDetails);
      setIsDetailModalVisible(true);
    } catch (error: any) {
      message.error(error.message || '获取工作空间详情失败');
    } finally {
      setLoading(false);
    }
  };


  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };

  const columns: ColumnsType<WorkspaceInfo> = [
    {
      title: 'Workspace ID',
      dataIndex: 'id',
      key: 'id',
      width: 150,
      render: (id: string) => (
        <code className="text-sm bg-gray-100 px-2 py-1 rounded">
          {id}
        </code>
      ),
    },
    {
      title: '显示名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string) => (
        <div className="font-medium">{name}</div>
      ),
    },
    {
      title: '创建者邮箱',
      dataIndex: 'ownerEmail',
      key: 'ownerEmail',
      render: (ownerEmail: string | null) => (
        <div className="text-gray-600">
          {ownerEmail || '未知'}
        </div>
      ),
    },
    {
      title: '剩余积分',
      dataIndex: 'totalCredits',
      key: 'totalCredits',
      width: 120,
      render: (totalCredits: number) => (
        <div className="flex items-center gap-1">
          <CreditCardOutlined className="text-orange-500" />
          <span className="font-mono">{formatNumber(totalCredits)}</span>
        </div>
      ),
    },
    {
      title: '用户数量',
      dataIndex: 'memberCount',
      key: 'memberCount',
      width: 100,
      render: (memberCount: number) => (
        <div className="flex items-center gap-1">
          <TeamOutlined className="text-blue-500" />
          <span>{memberCount}</span>
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (createdAt: Date | null) => 
        createdAt ? new Date(createdAt).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        }) : '-',
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (_, record) => (
        <Space>
          <Button
            icon={<EyeOutlined />}
            size="small"
            onClick={() => handleViewDetails(record.id)}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="w-full">
      <div className="mb-6">
        <Title level={2} className="flex items-center gap-2">
          <ShopOutlined />
          工作空间管理
        </Title>
        <Paragraph className="text-gray-600">
          管理系统中的所有工作空间和相关配置
        </Paragraph>
      </div>

      <Card className="w-full">
        <div className="mb-4 flex justify-between items-center">
          <div className="text-sm text-gray-500">
            共 {total} 个工作空间
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={workspaces}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={{ x: 1000 }}
          size="middle"
        />

        {total > pageSize && (
          <div className="mt-4 flex justify-center">
            <Pagination
              current={currentPage}
              total={total}
              pageSize={pageSize}
              onChange={handlePageChange}
              showSizeChanger={false}
              showQuickJumper
              showTotal={(total, range) => 
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }
            />
          </div>
        )}
      </Card>

      {/* 工作空间详情模态框 */}
      <Modal
        title="工作空间详情"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={700}
      >
        {selectedWorkspace && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="Workspace ID" span={2}>
              <code className="text-sm">{selectedWorkspace.id}</code>
            </Descriptions.Item>
            <Descriptions.Item label="显示名称" span={2}>
              {selectedWorkspace.name}
            </Descriptions.Item>
            <Descriptions.Item label="创建者邮箱">
              {selectedWorkspace.ownerEmail || '未知'}
            </Descriptions.Item>
            <Descriptions.Item label="创建者ID">
              <code className="text-sm">{selectedWorkspace.ownerId || '未知'}</code>
            </Descriptions.Item>
            <Descriptions.Item label="总成员数">
              <div className="flex items-center gap-1">
                <TeamOutlined className="text-blue-500" />
                {selectedWorkspace.totalMembers || 0}
              </div>
            </Descriptions.Item>
            <Descriptions.Item label="活跃成员数">
              <div className="flex items-center gap-1">
                <TeamOutlined className="text-green-500" />
                {selectedWorkspace.activeMembers || 0}
              </div>
            </Descriptions.Item>
            <Descriptions.Item label="总积分余额" span={2}>
              <div className="flex items-center gap-1">
                <CreditCardOutlined className="text-orange-500" />
                <span className="font-mono text-lg">{formatNumber(selectedWorkspace.totalCredits || 0)}</span>
              </div>
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {selectedWorkspace.createdAt 
                ? new Date(selectedWorkspace.createdAt).toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                  })
                : '-'
              }
            </Descriptions.Item>
            <Descriptions.Item label="最后更新">
              {selectedWorkspace.updatedAt 
                ? new Date(selectedWorkspace.updatedAt).toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                  })
                : '-'
              }
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
}
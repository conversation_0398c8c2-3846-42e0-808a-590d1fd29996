'use server';

import { db } from '@/app/db';
import { users, workspaces, userWorkspace } from '@/app/db/schema';
import { auth } from '@/auth';
import { eq, count, desc, sql } from 'drizzle-orm';

interface PaginationParams {
  page?: number;
  limit?: number;
}

interface WorkspaceInfo {
  id: string;
  name: string;
  ownerEmail: string | null;
  totalCredits: number;
  memberCount: number;
  createdAt: Date | null;
  plan: string;
}

interface WorkspaceListResponse {
  workspaces: WorkspaceInfo[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 获取工作空间列表
export async function getWorkspaceList({
  page = 1,
  limit = 20
}: PaginationParams = {}): Promise<WorkspaceListResponse> {
  try {
    // 验证管理员权限
    const session = await auth();
    if (!session?.user?.id) {
      throw new Error('未授权访问');
    }

    // 查询当前用户的管理员状态
    const currentUser = await db
      .select({ isAdmin: users.isAdmin })
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    if (!currentUser.length || !currentUser[0].isAdmin) {
      throw new Error('需要管理员权限');
    }

    // 构建查询条件
    const offset = (page - 1) * limit;

    // 查询工作空间列表，包含创建者邮箱、积分总额和成员数量
    const workspaceList = await db
      .select({
        id: workspaces.id,
        name: workspaces.name,
        plan: workspaces.plan,
        createdAt: workspaces.createdAt,
        ownerEmail: users.email,
        // 计算工作空间的总积分（所有用户积分余额之和）
        totalCredits: sql<number>`COALESCE(SUM(${userWorkspace.creditBalance}), 0)`,
        // 计算活跃成员数量
        memberCount: sql<number>`COUNT(CASE WHEN ${userWorkspace.isActive} = true THEN 1 END)`
      })
      .from(workspaces)
      .leftJoin(users, eq(workspaces.owner, users.id))
      .leftJoin(userWorkspace, eq(workspaces.id, userWorkspace.workspaceId))
      .groupBy(
        workspaces.id,
        workspaces.name,
        workspaces.plan,
        workspaces.createdAt,
        users.email
      )
      .orderBy(desc(workspaces.createdAt))
      .limit(limit)
      .offset(offset);

    // 查询总数
    const totalResult = await db
      .select({ count: count() })
      .from(workspaces);

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // 转换数据类型
    const formattedWorkspaces: WorkspaceInfo[] = workspaceList.map(workspace => ({
      id: workspace.id,
      name: workspace.name,
      ownerEmail: workspace.ownerEmail,
      totalCredits: Number(workspace.totalCredits),
      memberCount: Number(workspace.memberCount),
      createdAt: workspace.createdAt,
      plan: workspace.plan
    }));

    return {
      workspaces: formattedWorkspaces,
      total,
      page,
      limit,
      totalPages
    };
  } catch (error) {
    console.error('获取工作空间列表失败:', error);
    throw new Error('获取工作空间列表失败');
  }
}

// 获取工作空间详细信息
export async function getWorkspaceDetails(workspaceId: string) {
  try {
    // 验证管理员权限
    const session = await auth();
    if (!session?.user?.id) {
      throw new Error('未授权访问');
    }

    // 查询当前用户的管理员状态
    const currentUser = await db
      .select({ isAdmin: users.isAdmin })
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    if (!currentUser.length || !currentUser[0].isAdmin) {
      throw new Error('需要管理员权限');
    }

    // 查询工作空间详细信息
    const workspaceDetails = await db
      .select({
        id: workspaces.id,
        name: workspaces.name,
        plan: workspaces.plan,
        createdAt: workspaces.createdAt,
        updatedAt: workspaces.updatedAt,
        ownerEmail: users.email,
        ownerId: workspaces.owner
      })
      .from(workspaces)
      .leftJoin(users, eq(workspaces.owner, users.id))
      .where(eq(workspaces.id, workspaceId))
      .limit(1);

    if (!workspaceDetails.length) {
      throw new Error('工作空间不存在');
    }

    // 查询工作空间成员统计
    const memberStats = await db
      .select({
        totalMembers: count(),
        activeMembers: sql<number>`COUNT(CASE WHEN ${userWorkspace.isActive} = true THEN 1 END)`,
        totalCredits: sql<number>`COALESCE(SUM(${userWorkspace.creditBalance}), 0)`
      })
      .from(userWorkspace)
      .where(eq(userWorkspace.workspaceId, workspaceId));

    const stats = memberStats[0] || {
      totalMembers: 0,
      activeMembers: 0,
      totalCredits: 0
    };

    return {
      ...workspaceDetails[0],
      totalMembers: Number(stats.totalMembers),
      activeMembers: Number(stats.activeMembers),
      totalCredits: Number(stats.totalCredits)
    };
  } catch (error) {
    console.error('获取工作空间详情失败:', error);
    throw new Error('获取工作空间详情失败');
  }
}
'use server';

import { db } from '@/app/db';
import { users } from '@/app/db/schema';
import { auth } from '@/auth';
import { eq, ilike, count, desc } from 'drizzle-orm';

interface PaginationParams {
  page?: number;
  limit?: number;
  search?: string;
}

interface UserInfo {
  id: string;
  name: string | null;
  email: string | null;
  isAdmin: boolean | null;
  emailVerified: Date | null;
  image: string | null;
  createdAt: Date | null;
  dingdingUnionId: string | null;
  wecomUserId: string | null;
  feishuUserId: string | null;
}

interface UserListResponse {
  users: UserInfo[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 获取用户列表
export async function getUserList({
  page = 1,
  limit = 20,
  search = ''
}: PaginationParams = {}): Promise<UserListResponse> {
  try {
    // 验证管理员权限
    const session = await auth();
    if (!session?.user?.id) {
      throw new Error('未授权访问');
    }

    // 查询当前用户的管理员状态
    const currentUser = await db
      .select({ isAdmin: users.isAdmin })
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    if (!currentUser.length || !currentUser[0].isAdmin) {
      throw new Error('需要管理员权限');
    }

    // 构建查询条件
    const offset = (page - 1) * limit;
    
    // 如果有搜索条件，按邮箱搜索
    const whereCondition = search 
      ? ilike(users.email, `%${search}%`)
      : undefined;

    // 查询用户列表
    const userList = await db
      .select({
        id: users.id,
        name: users.name,
        email: users.email,
        isAdmin: users.isAdmin,
        emailVerified: users.emailVerified,
        image: users.image,
        createdAt: users.createdAt,
        dingdingUnionId: users.dingdingUnionId,
        wecomUserId: users.wecomUserId,
        feishuUserId: users.feishuUserId
      })
      .from(users)
      .where(whereCondition)
      .orderBy(desc(users.createdAt))
      .limit(limit)
      .offset(offset);

    // 查询总数
    const totalResult = await db
      .select({ count: count() })
      .from(users)
      .where(whereCondition);

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    return {
      users: userList,
      total,
      page,
      limit,
      totalPages
    };
  } catch (error) {
    console.error('获取用户列表失败:', error);
    throw new Error('获取用户列表失败');
  }
}

// 获取用户详细信息
export async function getUserDetails(userId: string) {
  try {
    // 验证管理员权限
    const session = await auth();
    if (!session?.user?.id) {
      throw new Error('未授权访问');
    }

    // 查询当前用户的管理员状态
    const currentUser = await db
      .select({ isAdmin: users.isAdmin })
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    if (!currentUser.length || !currentUser[0].isAdmin) {
      throw new Error('需要管理员权限');
    }

    // 查询用户详细信息
    const userDetails = await db
      .select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!userDetails.length) {
      throw new Error('用户不存在');
    }

    // 不返回敏感信息
    const { password, ...userInfo } = userDetails[0];

    return userInfo;
  } catch (error) {
    console.error('获取用户详情失败:', error);
    throw new Error('获取用户详情失败');
  }
}
'use client';

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Typography, 
  Table, 
  Input, 
  Button, 
  Tag, 
  Avatar, 
  Space, 
  Pagination, 
  message,
  Modal,
  Descriptions
} from 'antd';
import { 
  UserOutlined, 
  EyeOutlined,
  ClearOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { getUserList, getUserDetails } from './actions';

const { Title, Paragraph } = Typography;
const { Search } = Input;

interface UserInfo {
  id: string;
  name: string | null;
  email: string | null;
  isAdmin: boolean | null;
  emailVerified: Date | null;
  image: string | null;
  createdAt: Date | null;
  dingdingUnionId: string | null;
  wecomUserId: string | null;
  feishuUserId: string | null;
}

export default function UsersManagementPage() {
  const [users, setUsers] = useState<UserInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [pageSize] = useState(20);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);

  const loadUsers = async (page: number = currentPage, search: string = searchTerm) => {
    try {
      setLoading(true);
      const result = await getUserList({
        page,
        limit: pageSize,
        search: search.trim()
      });
      
      setUsers(result.users);
      setTotal(result.total);
      setCurrentPage(page);
    } catch (error: any) {
      message.error(error.message || '加载用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUsers(1, '');
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
    loadUsers(1, value);
  };

  const handleClearSearch = () => {
    setSearchTerm('');
    setCurrentPage(1);
    loadUsers(1, '');
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadUsers(page, searchTerm);
  };


  const handleViewDetails = async (userId: string) => {
    try {
      setLoading(true);
      const userDetails = await getUserDetails(userId);
      setSelectedUser(userDetails);
      setIsDetailModalVisible(true);
    } catch (error: any) {
      message.error(error.message || '获取用户详情失败');
    } finally {
      setLoading(false);
    }
  };

  const getProviderInfo = (user: UserInfo) => {
    const providers = [];
    if (user.dingdingUnionId) providers.push('钉钉');
    if (user.wecomUserId) providers.push('企业微信');
    if (user.feishuUserId) providers.push('飞书');
    return providers.length > 0 ? providers.join(', ') : '邮箱';
  };

  const columns: ColumnsType<UserInfo> = [
    {
      title: '头像',
      dataIndex: 'image',
      key: 'image',
      width: 60,
      render: (image: string | null) => (
        <Avatar 
          src={image} 
          icon={<UserOutlined />}
          size="small"
        />
      ),
    },
    {
      title: '用户信息',
      key: 'userInfo',
      render: (_, record) => (
        <div>
          <div className="font-medium">
            {record.name || record.email || '未知用户'}
          </div>
          <div className="text-gray-500 text-sm">
            {record.email}
          </div>
        </div>
      ),
    },
    {
      title: '登录方式',
      key: 'provider',
      render: (_, record) => (
        <Tag color="blue">{getProviderInfo(record)}</Tag>
      ),
    },
    {
      title: '全局管理员',
      dataIndex: 'isAdmin',
      key: 'isAdmin',
      width: 120,
      render: (isAdmin: boolean) => (
        <Tag color={isAdmin ? 'green' : 'default'}>
          {isAdmin ? '是' : '否'}
        </Tag>
      ),
    },
    {
      title: '注册时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (createdAt: Date | null) => 
        createdAt ? new Date(createdAt).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        }) : '-',
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (_, record) => (
        <Space>
          <Button
            icon={<EyeOutlined />}
            size="small"
            onClick={() => handleViewDetails(record.id)}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="w-full">
      <div className="mb-6">
        <Title level={2} className="flex items-center gap-2">
          <UserOutlined />
          用户管理
        </Title>
        <Paragraph className="text-gray-600">
          管理系统中的所有用户账户和权限
        </Paragraph>
      </div>

      <Card className="w-full">
        <div className="mb-4 flex flex-col sm:flex-row gap-4 justify-between">
          <div className="flex-1 max-w-md">
            <Search
              placeholder="搜索用户邮箱..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onSearch={handleSearch}
              onPressEnter={() => handleSearch(searchTerm)}
              suffix={
                searchTerm ? (
                  <Button
                    type="text"
                    size="small"
                    icon={<ClearOutlined />}
                    onClick={handleClearSearch}
                  />
                ) : null
              }
            />
          </div>
          <div className="text-sm text-gray-500">
            共 {total} 个用户
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={{ x: 800 }}
          size="middle"
        />

        {total > pageSize && (
          <div className="mt-4 flex justify-center">
            <Pagination
              current={currentPage}
              total={total}
              pageSize={pageSize}
              onChange={handlePageChange}
              showSizeChanger={false}
              showQuickJumper
              showTotal={(total, range) => 
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }
            />
          </div>
        )}
      </Card>

      {/* 用户详情模态框 */}
      <Modal
        title="用户详情"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={600}
      >
        {selectedUser && (
          <Descriptions column={1} bordered>
            <Descriptions.Item label="用户ID">
              <code className="text-sm">{selectedUser.id}</code>
            </Descriptions.Item>
            <Descriptions.Item label="用户名">
              {selectedUser.name || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="邮箱">
              {selectedUser.email || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="全局管理员">
              <Tag color={selectedUser.isAdmin ? 'green' : 'default'}>
                {selectedUser.isAdmin ? '是' : '否'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="注册时间">
              {selectedUser.createdAt 
                ? new Date(selectedUser.createdAt).toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                  })
                : '-'
              }
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
}
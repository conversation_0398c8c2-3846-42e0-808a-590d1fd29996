'use server';

import { db } from '@/app/db';
import { users, redemptionCodes, redemptionCodeUsages, workspaces } from '@/app/db/schema';
import { auth } from '@/auth';
import { eq, desc } from 'drizzle-orm';
import { RedemptionCodeNotFoundError, UnauthorizedError } from './errors';

// UUID 验证正则表达式
const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

// 验证 UUID 格式
function isValidUuid(id: string): boolean {
  if (!id || typeof id !== 'string') {
    return false;
  }
  return UUID_REGEX.test(id);
}

interface RedemptionCodeDetails {
  id: string;
  code: string;
  expiresAt: Date;
  usageLimit: number;
  usedCount: number;
  creditAmount: number;
  isActive: boolean;
  createdAt: Date | null;
  updatedAt: Date | null;
}

interface RedemptionCodeUsage {
  id: string;
  userEmail: string;
  workspaceId: string;
  workspaceName?: string;
  creditAmount: number;
  usedAt: Date | null;
}

// 验证管理员权限的通用函数
async function checkAdminPermission() {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      throw new UnauthorizedError('未登录或会话已过期');
    }

    const currentUser = await db
      .select({ isAdmin: users.isAdmin })
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    if (!currentUser.length || !currentUser[0].isAdmin) {
      throw new UnauthorizedError('需要管理员权限');
    }

    return session.user.id;
  } catch (error) {
    if (error instanceof UnauthorizedError) {
      throw error;
    }
    // 对于数据库错误等其他错误，记录详细日志但不暴露给客户端
    console.error('权限检查失败:', error);
    throw new UnauthorizedError('权限验证失败');
  }
}

// 获取兑换码详情
export async function getRedemptionCodeDetails(codeId: string): Promise<RedemptionCodeDetails> {
  try {
    // 验证管理员权限
    await checkAdminPermission();

    // 验证 UUID 格式
    if (!isValidUuid(codeId)) {
      throw new RedemptionCodeNotFoundError('兑换码ID格式无效');
    }

    // 查询兑换码详细信息
    const codeDetails = await db
      .select({
        id: redemptionCodes.id,
        code: redemptionCodes.code,
        expiresAt: redemptionCodes.expiresAt,
        usageLimit: redemptionCodes.usageLimit,
        usedCount: redemptionCodes.usedCount,
        creditAmount: redemptionCodes.creditAmount,
        isActive: redemptionCodes.isActive,
        createdAt: redemptionCodes.createdAt,
        updatedAt: redemptionCodes.updatedAt
      })
      .from(redemptionCodes)
      .where(eq(redemptionCodes.id, codeId))
      .limit(1);

    if (!codeDetails.length) {
      throw new RedemptionCodeNotFoundError('指定的兑换码不存在');
    }

    const code = codeDetails[0];
    return {
      ...code,
      isActive: code.isActive ?? false,
      creditAmount: Number(code.creditAmount)
    };
  } catch (error) {
    // 对于已知的业务错误，不记录详细日志
    if (error instanceof RedemptionCodeNotFoundError || error instanceof UnauthorizedError) {
      throw error;
    }
    
    // 对于数据库错误等未知错误，记录详细日志
    console.error('获取兑换码详情时发生未知错误:', {
      codeId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    
    // 抛出通用错误，避免泄露系统信息
    throw new Error('获取兑换码详情失败，请稍后重试');
  }
}

// 获取兑换码使用记录
export async function getRedemptionCodeUsages(codeId: string): Promise<RedemptionCodeUsage[]> {
  try {
    // 验证管理员权限
    await checkAdminPermission();

    // 验证 UUID 格式
    if (!isValidUuid(codeId)) {
      throw new RedemptionCodeNotFoundError('兑换码ID格式无效');
    }

    // 先验证兑换码是否存在
    const codeExists = await db
      .select({ id: redemptionCodes.id })
      .from(redemptionCodes)
      .where(eq(redemptionCodes.id, codeId))
      .limit(1);

    if (!codeExists.length) {
      throw new RedemptionCodeNotFoundError('指定的兑换码不存在');
    }

    // 查询兑换码使用记录，包含用户邮箱和工作空间信息
    const usageRecords = await db
      .select({
        id: redemptionCodeUsages.id,
        userId: redemptionCodeUsages.userId,
        workspaceId: redemptionCodeUsages.workspaceId,
        creditAmount: redemptionCodeUsages.creditAmount,
        usedAt: redemptionCodeUsages.usedAt,
        userEmail: users.email,
        workspaceName: workspaces.name
      })
      .from(redemptionCodeUsages)
      .leftJoin(users, eq(redemptionCodeUsages.userId, users.id))
      .leftJoin(workspaces, eq(redemptionCodeUsages.workspaceId, workspaces.id))
      .where(eq(redemptionCodeUsages.redemptionCodeId, codeId))
      .orderBy(desc(redemptionCodeUsages.usedAt));

    // 格式化返回数据
    return usageRecords.map(record => ({
      id: record.id,
      userEmail: record.userEmail || '未知用户',
      workspaceId: record.workspaceId,
      workspaceName: record.workspaceName || '未知工作空间',
      creditAmount: Number(record.creditAmount),
      usedAt: record.usedAt
    }));
  } catch (error) {
    // 对于已知的业务错误，不记录详细日志
    if (error instanceof RedemptionCodeNotFoundError || error instanceof UnauthorizedError) {
      throw error;
    }
    
    // 对于数据库错误等未知错误，记录详细日志
    console.error('获取兑换码使用记录时发生未知错误:', {
      codeId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    
    // 抛出通用错误，避免泄露系统信息
    throw new Error('获取兑换码使用记录失败，请稍后重试');
  }
}
// 自定义错误类型
export class RedemptionCodeNotFoundError extends Error {
  constructor(message: string = '兑换码不存在') {
    super(message);
    this.name = 'RedemptionCodeNotFoundError';
  }
}

export class UnauthorizedError extends Error {
  constructor(message: string = '未授权访问') {
    super(message);
    this.name = 'UnauthorizedError';
  }
}

export class InvalidUuidError extends Error {
  constructor(message: string = '无效的兑换码ID格式') {
    super(message);
    this.name = 'InvalidUuidError';
  }
}
'use client';

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Typography, 
  Table, 
  Tag, 
  Space, 
  message,
  Descriptions,
  Button,
  Progress,
  Spin,
  Alert,
  Empty
} from 'antd';
import { 
  GiftOutlined, 
  ArrowLeftOutlined,
  CreditCardOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  UserOutlined,
  ShopOutlined,
  HistoryOutlined,
  FileSearchOutlined,
  LockOutlined
} from '@ant-design/icons';
import { useRouter, useParams } from 'next/navigation';
import type { ColumnsType } from 'antd/es/table';
import { 
  getRedemptionCodeDetails, 
  getRedemptionCodeUsages
} from './actions';
import { 
  RedemptionCodeNotFoundError,
  UnauthorizedError 
} from './errors';
import Link from 'next/link';

const { Title, Paragraph } = Typography;

interface RedemptionCodeDetails {
  id: string;
  code: string;
  expiresAt: Date;
  usageLimit: number;
  usedCount: number;
  creditAmount: number;
  isActive: boolean;
  createdAt: Date | null;
  updatedAt: Date | null;
}

interface RedemptionCodeUsage {
  id: string;
  userEmail: string;
  workspaceId: string;
  workspaceName?: string;
  creditAmount: number;
  usedAt: Date | null;
}

export default function RedemptionCodeDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const codeId = params?.codeId as string;

  const [codeDetails, setCodeDetails] = useState<RedemptionCodeDetails | null>(null);
  const [usageRecords, setUsageRecords] = useState<RedemptionCodeUsage[]>([]);
  const [loading, setLoading] = useState(true);
  const [usageLoading, setUsageLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [notFound, setNotFound] = useState(false);
  const [unauthorized, setUnauthorized] = useState(false);

  const loadCodeDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      setNotFound(false);
      setUnauthorized(false);
      
      const details = await getRedemptionCodeDetails(codeId);
      setCodeDetails(details);
    } catch (error: any) {
      console.log('加载兑换码详情时出错:', error.name, error.message);
      
      if (error instanceof RedemptionCodeNotFoundError) {
        setNotFound(true);
        return;
      }
      
      if (error instanceof UnauthorizedError) {
        setUnauthorized(true);
        return;
      }
      
      // 对于其他错误，显示错误信息
      setError(error.message || '获取兑换码详情失败');
    } finally {
      setLoading(false);
    }
  };

  const loadUsageRecords = async () => {
    try {
      setUsageLoading(true);
      const records = await getRedemptionCodeUsages(codeId);
      setUsageRecords(records);
    } catch (error: any) {
      console.log('加载使用记录时出错:', error.name, error.message);
      
      if (error instanceof RedemptionCodeNotFoundError) {
        setNotFound(true);
        return;
      }
      
      if (error instanceof UnauthorizedError) {
        setUnauthorized(true);
        return;
      }
      
      // 对于其他错误，如果详情还没有加载成功，则不显示错误（避免重复报错）
      if (!codeDetails) {
        console.error('获取使用记录失败:', error);
      }
    } finally {
      setUsageLoading(false);
    }
  };

  useEffect(() => {
    if (codeId) {
      loadCodeDetails();
      // 只有详情加载成功后才加载使用记录
      loadUsageRecords();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [codeId]);

  const isExpired = (expiresAt: Date) => {
    return new Date() > new Date(expiresAt);
  };

  const getStatusColor = (code: RedemptionCodeDetails) => {
    if (!code.isActive) return 'red';
    if (isExpired(code.expiresAt)) return 'orange';
    if (code.usedCount >= code.usageLimit) return 'default';
    return 'green';
  };

  const getStatusText = (code: RedemptionCodeDetails) => {
    if (!code.isActive) return '已禁用';
    if (isExpired(code.expiresAt)) return '已过期';
    if (code.usedCount >= code.usageLimit) return '已用完';
    return '可用';
  };

  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };

  const getUsagePercent = (usedCount: number, usageLimit: number) => {
    return Math.round((usedCount / usageLimit) * 100);
  };

  const usageColumns: ColumnsType<RedemptionCodeUsage> = [
    {
      title: '使用者邮箱',
      dataIndex: 'userEmail',
      key: 'userEmail',
      width: 200,
      render: (email: string) => (
        <div className="flex items-center gap-2">
          <UserOutlined className="text-blue-500" />
          <span>{email}</span>
        </div>
      ),
    },
    {
      title: '工作空间',
      key: 'workspace',
      width: 200,
      render: (_, record) => (
        <div className="flex items-center gap-2">
          <ShopOutlined className="text-green-500" />
          <div>
            <div className="font-medium">{record.workspaceName}</div>
            <div className="text-xs text-gray-500">{record.workspaceId}</div>
          </div>
        </div>
      ),
    },
    {
      title: '获取积分',
      dataIndex: 'creditAmount',
      key: 'creditAmount',
      width: 120,
      render: (credits: number) => (
        <div className="flex items-center gap-1">
          <CreditCardOutlined className="text-orange-500" />
          <span className="font-mono font-medium">{formatNumber(credits)}</span>
        </div>
      ),
    },
    {
      title: '使用时间',
      dataIndex: 'usedAt',
      key: 'usedAt',
      width: 180,
      render: (usedAt: Date | null) => (
        <div className="flex items-center gap-2">
          <HistoryOutlined className="text-gray-500" />
          <span className="text-sm">
            {usedAt 
              ? new Date(usedAt).toLocaleString('zh-CN', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  hour12: false
                })
              : '-'
            }
          </span>
        </div>
      ),
    },
  ];

  if (loading) {
    return (
      <div className="w-full flex justify-center items-center min-h-96">
        <Spin size="large" />
        <span className="ml-3 text-gray-600">加载兑换码详情...</span>
      </div>
    );
  }

  // 显示授权错误页面
  if (unauthorized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full text-center">
          <div className="mb-8">
            <LockOutlined className="text-6xl text-red-400 mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 mb-4">访问被拒绝</h1>
            <h2 className="text-xl font-semibold text-gray-700 mb-2">401 - 未授权访问</h2>
            <p className="text-gray-500">
              您没有权限访问此页面，请确保您已登录且具有管理员权限。
            </p>
          </div>
          
          <div className="space-y-4">
            <Link href="/login">
              <Button type="primary" size="large">
                重新登录
              </Button>
            </Link>
            
            <Link href="/administrator">
              <Button size="large">
                返回管理后台
              </Button>
            </Link>
          </div>
          
          <div className="mt-8 text-sm text-gray-400">
            <p>如果您认为这是一个错误，请联系系统管理员。</p>
          </div>
        </div>
      </div>
    );
  }

  // 显示 404 页面
  if (notFound) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full text-center">
          <div className="mb-8">
            <FileSearchOutlined className="text-6xl text-gray-400 mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 mb-4">兑换码不存在</h1>
            <h2 className="text-xl font-semibold text-gray-700 mb-2">404 - 页面未找到</h2>
            <p className="text-gray-500">
              您访问的兑换码不存在，可能已被删除或您输入了错误的兑换码ID。
            </p>
          </div>
          
          <div className="space-y-4">
            <Link href="/administrator/redemption-code">
              <Button type="primary" size="large" icon={<ArrowLeftOutlined />}>
                返回兑换码列表
              </Button>
            </Link>
            
            <Link href="/administrator">
              <Button size="large">
                返回管理后台
              </Button>
            </Link>
          </div>
          
          <div className="mt-8 text-sm text-gray-400">
            <p>如果您认为这是一个错误，请联系系统管理员。</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !codeDetails) {
    return (
      <div className="w-full">
        <div className="mb-6">
          <div className="flex items-center gap-3 mb-4">
            <Link href="/administrator/redemption-code">
              <Button icon={<ArrowLeftOutlined />} type="link">
                返回兑换码列表
              </Button>
            </Link>
          </div>
        </div>
        <Alert
          message="页面加载失败"
          description={error || '兑换码不存在或无法访问'}
          type="error"
          showIcon
          action={
            <Space>
              <Button size="small" onClick={() => router.back()}>
                返回列表
              </Button>
              <Button size="small" type="primary" onClick={loadCodeDetails}>
                重新加载
              </Button>
            </Space>
          }
        />
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* 页面头部 */}
      <div className="mb-6">
        <div className="flex items-center gap-3 mb-4">
          <Link href="/administrator/redemption-code">
            <Button icon={<ArrowLeftOutlined />} type="link">
              返回兑换码列表
            </Button>
          </Link>
        </div>
        <Title level={2} className="flex items-center gap-2">
          <GiftOutlined />
          兑换码详情
        </Title>
        <Paragraph className="text-gray-600">
          查看兑换码的详细信息和使用记录
        </Paragraph>
      </div>

      {/* 兑换码基本信息 */}
      <Card className="mb-6">
        <Title level={4} className="mb-4">基本信息</Title>
        <Descriptions column={2} bordered>
          <Descriptions.Item label="兑换码ID" span={2}>
            <code className="text-sm">{codeDetails.id}</code>
          </Descriptions.Item>
          <Descriptions.Item label="兑换码" span={2}>
            <code className="text-xl bg-gray-100 px-4 py-2 rounded font-mono font-bold">
              {codeDetails.code}
            </code>
          </Descriptions.Item>
          <Descriptions.Item label="状态">
            <Tag 
              color={getStatusColor(codeDetails)} 
              icon={
                codeDetails.isActive && !isExpired(codeDetails.expiresAt) && codeDetails.usedCount < codeDetails.usageLimit ? 
                <CheckCircleOutlined /> : <CloseCircleOutlined />
              }
            >
              {getStatusText(codeDetails)}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="是否激活">
            <Tag color={codeDetails.isActive ? 'green' : 'red'}>
              {codeDetails.isActive ? '激活' : '禁用'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="积分价值">
            <div className="flex items-center gap-2">
              <CreditCardOutlined className="text-orange-500" />
              <span className="font-mono text-lg font-medium">{formatNumber(codeDetails.creditAmount)}</span>
            </div>
          </Descriptions.Item>
          <Descriptions.Item label="使用次数限制">
            <span className="font-mono">{codeDetails.usageLimit}</span>
          </Descriptions.Item>
          <Descriptions.Item label="已使用次数">
            <span className="font-mono">{codeDetails.usedCount}</span>
          </Descriptions.Item>
          <Descriptions.Item label="使用进度">
            <div className="w-32">
              <Progress 
                percent={getUsagePercent(codeDetails.usedCount, codeDetails.usageLimit)}
                strokeColor={codeDetails.usedCount >= codeDetails.usageLimit ? '#ff4d4f' : '#52c41a'}
                size="small"
              />
            </div>
          </Descriptions.Item>
          <Descriptions.Item label="有效期" span={2}>
            <div className={`flex items-center gap-2 ${isExpired(codeDetails.expiresAt) ? 'text-red-500' : ''}`}>
              <CalendarOutlined />
              <span>
                {new Date(codeDetails.expiresAt).toLocaleString('zh-CN', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  hour12: false
                })}
              </span>
              {isExpired(codeDetails.expiresAt) && (
                <Tag color="red">已过期</Tag>
              )}
            </div>
          </Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {codeDetails.createdAt 
              ? new Date(codeDetails.createdAt).toLocaleString('zh-CN', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  hour12: false
                })
              : '-'
            }
          </Descriptions.Item>
          <Descriptions.Item label="最后更新">
            {codeDetails.updatedAt 
              ? new Date(codeDetails.updatedAt).toLocaleString('zh-CN', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  hour12: false
                })
              : '-'
            }
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 使用记录 */}
      <Card>
        <div className="flex items-center justify-between mb-4">
          <Title level={4} className="mb-0">使用记录</Title>
          <div className="text-sm text-gray-500">
            共 {usageRecords.length} 条记录
          </div>
        </div>
        
        {usageLoading ? (
          <div className="flex justify-center items-center py-8">
            <Spin size="large" />
            <span className="ml-3 text-gray-600">加载使用记录...</span>
          </div>
        ) : usageRecords.length === 0 ? (
          <Empty 
            description="暂无使用记录" 
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          <Table
            columns={usageColumns}
            dataSource={usageRecords}
            rowKey="id"
            pagination={false}
            scroll={{ x: 700 }}
            size="middle"
          />
        )}
      </Card>
    </div>
  );
}
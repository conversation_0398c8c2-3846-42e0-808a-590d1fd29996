'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Table,
  Tag,
  Space,
  Pagination,
  message,
  Modal,
  Form,
  Input,
  InputNumber,
  DatePicker,
  Button,
  Progress
} from 'antd';
import {
  GiftOutlined,
  ArrowRightOutlined,
  PlusOutlined,
  ReloadOutlined,
  CreditCardOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { getRedemptionCodeList, createRedemptionCode, generateRandomCode } from './actions';
import Link from 'next/link';
import dayjs from 'dayjs';

const { Title, Paragraph } = Typography;

interface RedemptionCodeInfo {
  id: string;
  code: string;
  expiresAt: Date;
  usageLimit: number;
  usedCount: number;
  creditAmount: number;
  isActive: boolean;
  createdAt: Date | null;
  updatedAt: Date | null;
}

export default function RedemptionCodeManagementPage() {
  const [redemptionCodes, setRedemptionCodes] = useState<RedemptionCodeInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [pageSize] = useState(20);
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [createLoading, setCreateLoading] = useState(false);
  const [form] = Form.useForm();

  const loadRedemptionCodes = async (page: number = currentPage) => {
    try {
      setLoading(true);
      const result = await getRedemptionCodeList({
        page,
        limit: pageSize
      });

      setRedemptionCodes(result.redemptionCodes);
      setTotal(result.total);
      setCurrentPage(page);
    } catch (error: any) {
      message.error(error.message || '加载兑换码列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadRedemptionCodes(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 处理创建兑换码表单提交
  const handleCreateSubmit = async (values: any) => {
    try {
      setCreateLoading(true);
      const params = {
        code: values.code.toUpperCase(),
        expiresAt: values.expiresAt.toDate(),
        usageLimit: values.usageLimit,
        creditAmount: values.creditAmount
      };

      await createRedemptionCode(params);
      message.success('兑换码创建成功');
      handleCloseModal();
      loadRedemptionCodes(currentPage); // 刷新列表
    } catch (error: any) {
      message.error(error.message || '创建兑换码失败');
    } finally {
      setCreateLoading(false);
    }
  };

  // 关闭创建模态框
  const handleCloseModal = () => {
    setIsCreateModalVisible(false);
    form.resetFields();
  };

  // 生成随机兑换码
  const handleGenerateCode = async () => {
    const randomCode = await generateRandomCode();
    form.setFieldValue('code', randomCode);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadRedemptionCodes(page);
  };


  const isExpired = (expiresAt: Date) => {
    return new Date() > new Date(expiresAt);
  };

  const getStatusColor = (code: RedemptionCodeInfo) => {
    if (!code.isActive) return 'red';
    if (isExpired(code.expiresAt)) return 'orange';
    if (code.usedCount >= code.usageLimit) return 'default';
    return 'green';
  };

  const getStatusText = (code: RedemptionCodeInfo) => {
    if (!code.isActive) return '已禁用';
    if (isExpired(code.expiresAt)) return '已过期';
    if (code.usedCount >= code.usageLimit) return '已用完';
    return '可用';
  };

  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };

  const getUsagePercent = (usedCount: number, usageLimit: number) => {
    return Math.round((usedCount / usageLimit) * 100);
  };

  const columns: ColumnsType<RedemptionCodeInfo> = [
    {
      title: '兑换码',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      render: (code: string) => (
        <code className="text-sm bg-gray-100 px-2 py-1 rounded font-mono">
          {code}
        </code>
      ),
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      render: (_, record) => (
        <Tag color={getStatusColor(record)} icon={
          record.isActive && !isExpired(record.expiresAt) && record.usedCount < record.usageLimit ?
            <CheckCircleOutlined /> : <CloseCircleOutlined />
        }>
          {getStatusText(record)}
        </Tag>
      ),
    },
    {
      title: '积分价值',
      dataIndex: 'creditAmount',
      key: 'creditAmount',
      width: 120,
      render: (credits: number) => (
        <div className="flex items-center gap-1">
          <CreditCardOutlined className="text-orange-500" />
          <span className="font-mono">{formatNumber(credits)}</span>
        </div>
      ),
    },
    {
      title: '使用情况',
      key: 'usage',
      width: 150,
      render: (_, record) => (
        <div>
          <div className="text-sm mb-1">
            {record.usedCount} / {record.usageLimit}
          </div>
          <Progress
            percent={getUsagePercent(record.usedCount, record.usageLimit)}
            size="small"
            strokeColor={record.usedCount >= record.usageLimit ? '#ff4d4f' : '#52c41a'}
          />
        </div>
      ),
    },
    {
      title: '有效期',
      dataIndex: 'expiresAt',
      key: 'expiresAt',
      width: 180,
      render: (expiresAt: Date) => (
        <div className={`flex items-center gap-1 ${isExpired(expiresAt) ? 'text-red-500' : ''}`}>
          <CalendarOutlined />
          <span className="text-sm">
            {new Date(expiresAt).toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            })}
          </span>
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (createdAt: Date | null) =>
        createdAt ? new Date(createdAt).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        }) : '-',
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (_, record) => (
        <Space>
          <Link href={`/administrator/redemption-code/${record.id}`}>
            <Button
              icon={<ArrowRightOutlined />}
              size="small"
              type="primary"
            >
              查看详情
            </Button>
          </Link>
        </Space>
      ),
    },
  ];

  return (
    <div className="w-full">
      <div className="mb-6">
        <Title level={2} className="flex items-center gap-2">
          <GiftOutlined />
          兑换码管理
        </Title>
        <Paragraph className="text-gray-600">
          管理系统中的所有兑换码的生成和使用情况
        </Paragraph>
      </div>

      <Card className="w-full">
        <div className="mb-4 flex justify-between items-center">
          <div className="text-sm text-gray-500">
            共 {total} 个兑换码
          </div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setIsCreateModalVisible(true)}
          >
            创建兑换码
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={redemptionCodes}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={{ x: 1000 }}
          size="middle"
        />

        {total > pageSize && (
          <div className="mt-4 flex justify-center">
            <Pagination
              current={currentPage}
              total={total}
              pageSize={pageSize}
              onChange={handlePageChange}
              showSizeChanger={false}
              showQuickJumper
              showTotal={(total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }
            />
          </div>
        )}
      </Card>

      {/* 创建兑换码 Modal */}
      <Modal
        title="创建兑换码"
        open={isCreateModalVisible}
        onCancel={handleCloseModal}
        footer={null}
        width={600}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateSubmit}
          className="mt-4"
        >
          <Form.Item
            label="兑换码"
            name="code"
            rules={[
              { required: true, message: '请输入兑换码' },
              { len: 8, message: '兑换码必须为8位字符' },
              { pattern: /^[A-Z0-9]+$/, message: '兑换码只能包含大写字母和数字' }
            ]}
          >
            <Input
              placeholder="请输入8位兑换码"
              maxLength={8}
              style={{ textTransform: 'uppercase' }}
              addonAfter={
                <Button
                  type="text"
                  icon={<ReloadOutlined />}
                  onClick={handleGenerateCode}
                  size="small"
                >
                  随机生成
                </Button>
              }
            />
          </Form.Item>

          <Form.Item
            label="有效期"
            name="expiresAt"
            rules={[
              { required: true, message: '请选择有效期' },
              {
                validator: (_, value) => {
                  if (value && value.isBefore(dayjs())) {
                    return Promise.reject(new Error('有效期不能早于当前时间'));
                  }
                  return Promise.resolve();
                }
              }
            ]}
          >
            <DatePicker
              showTime
              placeholder="请选择兑换码有效期"
              style={{ width: '100%' }}
              format="YYYY-MM-DD HH:mm:ss"
              disabledDate={(current) => current && current < dayjs().startOf('day')}
            />
          </Form.Item>

          <Form.Item
            label="可使用次数"
            name="usageLimit"
            rules={[
              { required: true, message: '请输入可使用次数' },
              { type: 'number', min: 1, message: '使用次数必须大于0' }
            ]}
          >
            <InputNumber
              placeholder="请输入可使用次数"
              style={{ width: '100%' }}
              min={1}
              max={10000}
            />
          </Form.Item>

          <Form.Item
            label="兑换积分数"
            name="creditAmount"
            rules={[
              { required: true, message: '请输入兑换积分数' },
              { type: 'number', min: 1, message: '积分数必须大于0' }
            ]}
          >
            <InputNumber
              placeholder="请输入兑换积分数"
              style={{ width: '100%' }}
              min={1}
              max={1000000}
              controls
              step={100}
            />
          </Form.Item>

          <Form.Item className="mb-0 mt-6">
            <div className="flex justify-end gap-3">
              <Button onClick={handleCloseModal}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createLoading}
              >
                创建兑换码
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Modal>

    </div>
  );
}
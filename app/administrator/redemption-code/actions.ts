'use server';

import { db } from '@/app/db';
import { users, redemptionCodes } from '@/app/db/schema';
import { auth } from '@/auth';
import { eq, count, desc } from 'drizzle-orm';

interface PaginationParams {
  page?: number;
  limit?: number;
}

interface RedemptionCodeInfo {
  id: string;
  code: string;
  expiresAt: Date;
  usageLimit: number;
  usedCount: number;
  creditAmount: number;
  isActive: boolean;
  createdAt: Date | null;
  updatedAt: Date | null;
}

interface RedemptionCodeListResponse {
  redemptionCodes: RedemptionCodeInfo[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

interface CreateRedemptionCodeParams {
  code: string;
  expiresAt: Date;
  usageLimit: number;
  creditAmount: number;
}

// 获取兑换码列表
export async function getRedemptionCodeList({
  page = 1,
  limit = 20
}: PaginationParams = {}): Promise<RedemptionCodeListResponse> {
  try {
    // 验证管理员权限
    const session = await auth();
    if (!session?.user?.id) {
      throw new Error('未授权访问');
    }

    // 查询当前用户的管理员状态
    const currentUser = await db
      .select({ isAdmin: users.isAdmin })
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    if (!currentUser.length || !currentUser[0].isAdmin) {
      throw new Error('需要管理员权限');
    }

    // 构建查询条件
    const offset = (page - 1) * limit;

    // 查询兑换码列表
    const redemptionCodeList = await db
      .select({
        id: redemptionCodes.id,
        code: redemptionCodes.code,
        expiresAt: redemptionCodes.expiresAt,
        usageLimit: redemptionCodes.usageLimit,
        usedCount: redemptionCodes.usedCount,
        creditAmount: redemptionCodes.creditAmount,
        isActive: redemptionCodes.isActive,
        createdAt: redemptionCodes.createdAt,
        updatedAt: redemptionCodes.updatedAt
      })
      .from(redemptionCodes)
      .orderBy(desc(redemptionCodes.createdAt))
      .limit(limit)
      .offset(offset);

    // 查询总数
    const totalResult = await db
      .select({ count: count() })
      .from(redemptionCodes);

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // 转换数据类型
    const formattedRedemptionCodes: RedemptionCodeInfo[] = redemptionCodeList.map(code => ({
      id: code.id,
      code: code.code,
      expiresAt: code.expiresAt,
      usageLimit: code.usageLimit,
      usedCount: code.usedCount,
      creditAmount: Number(code.creditAmount),
      isActive: code.isActive || false,
      createdAt: code.createdAt,
      updatedAt: code.updatedAt
    }));

    return {
      redemptionCodes: formattedRedemptionCodes,
      total,
      page,
      limit,
      totalPages
    };
  } catch (error) {
    console.error('获取兑换码列表失败:', error);
    throw new Error('获取兑换码列表失败');
  }
}

// 获取兑换码详细信息
export async function getRedemptionCodeDetails(codeId: string) {
  try {
    // 验证管理员权限
    const session = await auth();
    if (!session?.user?.id) {
      throw new Error('未授权访问');
    }

    // 查询当前用户的管理员状态
    const currentUser = await db
      .select({ isAdmin: users.isAdmin })
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    if (!currentUser.length || !currentUser[0].isAdmin) {
      throw new Error('需要管理员权限');
    }

    // 查询兑换码详细信息
    const codeDetails = await db
      .select({
        id: redemptionCodes.id,
        code: redemptionCodes.code,
        expiresAt: redemptionCodes.expiresAt,
        usageLimit: redemptionCodes.usageLimit,
        usedCount: redemptionCodes.usedCount,
        creditAmount: redemptionCodes.creditAmount,
        isActive: redemptionCodes.isActive,
        createdAt: redemptionCodes.createdAt,
        updatedAt: redemptionCodes.updatedAt
      })
      .from(redemptionCodes)
      .where(eq(redemptionCodes.id, codeId))
      .limit(1);

    if (!codeDetails.length) {
      throw new Error('兑换码不存在');
    }

    const code = codeDetails[0];
    return {
      ...code,
      creditAmount: Number(code.creditAmount)
    };
  } catch (error) {
    console.error('获取兑换码详情失败:', error);
    throw new Error('获取兑换码详情失败');
  }
}

// 创建兑换码
export async function createRedemptionCode(params: CreateRedemptionCodeParams): Promise<RedemptionCodeInfo> {
  try {
    // 验证管理员权限
    const session = await auth();
    if (!session?.user?.id) {
      throw new Error('未授权访问');
    }

    // 查询当前用户的管理员状态
    const currentUser = await db
      .select({ isAdmin: users.isAdmin })
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    if (!currentUser.length || !currentUser[0].isAdmin) {
      throw new Error('需要管理员权限');
    }

    // 验证兑换码是否已存在
    const existingCode = await db
      .select({ id: redemptionCodes.id })
      .from(redemptionCodes)
      .where(eq(redemptionCodes.code, params.code))
      .limit(1);

    if (existingCode.length > 0) {
      throw new Error('兑换码已存在');
    }

    // 创建兑换码
    const newCode = await db
      .insert(redemptionCodes)
      .values({
        code: params.code,
        expiresAt: params.expiresAt,
        usageLimit: params.usageLimit,
        creditAmount: params.creditAmount,
        usedCount: 0,
        isActive: true
      })
      .returning();

    if (!newCode.length) {
      throw new Error('创建兑换码失败');
    }

    // 格式化返回数据
    const result = newCode[0];
    return {
      id: result.id,
      code: result.code,
      expiresAt: result.expiresAt,
      usageLimit: result.usageLimit,
      usedCount: result.usedCount,
      creditAmount: Number(result.creditAmount),
      isActive: result.isActive ?? false,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt
    };
  } catch (error) {
    console.error('创建兑换码失败:', error);
    throw error;
  }
}

// 生成随机兑换码
export async function generateRandomCode(): Promise<string> {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
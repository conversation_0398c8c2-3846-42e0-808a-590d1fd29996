'use server';

import { db } from '@/app/db';
import { users, workspaces, purchaseOrders } from '@/app/db/schema';
import { auth } from '@/auth';
import { eq, count, desc } from 'drizzle-orm';

interface PaginationParams {
  page?: number;
  limit?: number;
}

interface OrderInfo {
  id: string;
  workspaceId: string;
  workspaceName: string;
  userId: string;
  userEmail: string | null;
  packageType: string;
  stripeSessionId: string;
  status: string;
  amountPaidCents: number;
  currency: string;
  creditsPurchased: number;
  completedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

interface OrderListResponse {
  orders: OrderInfo[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 获取订单列表
export async function getOrderList({
  page = 1,
  limit = 20
}: PaginationParams = {}): Promise<OrderListResponse> {
  try {
    // 验证管理员权限
    const session = await auth();
    if (!session?.user?.id) {
      throw new Error('未授权访问');
    }

    // 查询当前用户的管理员状态
    const currentUser = await db
      .select({ isAdmin: users.isAdmin })
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    if (!currentUser.length || !currentUser[0].isAdmin) {
      throw new Error('需要管理员权限');
    }

    // 构建查询条件
    const offset = (page - 1) * limit;

    // 查询订单列表，包含用户邮箱和工作空间名称
    const orderList = await db
      .select({
        id: purchaseOrders.id,
        workspaceId: purchaseOrders.workspaceId,
        workspaceName: workspaces.name,
        userId: purchaseOrders.userId,
        userEmail: users.email,
        packageType: purchaseOrders.packageType,
        stripeSessionId: purchaseOrders.stripeSessionId,
        status: purchaseOrders.status,
        amountPaidCents: purchaseOrders.amountPaidCents,
        currency: purchaseOrders.currency,
        creditsPurchased: purchaseOrders.creditsPurchased,
        completedAt: purchaseOrders.completedAt,
        createdAt: purchaseOrders.createdAt,
        updatedAt: purchaseOrders.updatedAt
      })
      .from(purchaseOrders)
      .leftJoin(users, eq(purchaseOrders.userId, users.id))
      .leftJoin(workspaces, eq(purchaseOrders.workspaceId, workspaces.id))
      .orderBy(desc(purchaseOrders.createdAt))
      .limit(limit)
      .offset(offset);

    // 查询总数
    const totalResult = await db
      .select({ count: count() })
      .from(purchaseOrders);

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // 转换数据类型
    const formattedOrders: OrderInfo[] = orderList.map(order => ({
      id: order.id,
      workspaceId: order.workspaceId,
      workspaceName: order.workspaceName || '未知工作空间',
      userId: order.userId,
      userEmail: order.userEmail,
      packageType: order.packageType,
      stripeSessionId: order.stripeSessionId,
      status: order.status,
      amountPaidCents: order.amountPaidCents,
      currency: order.currency,
      creditsPurchased: Number(order.creditsPurchased),
      completedAt: order.completedAt,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt
    }));

    return {
      orders: formattedOrders,
      total,
      page,
      limit,
      totalPages
    };
  } catch (error) {
    console.error('获取订单列表失败:', error);
    throw new Error('获取订单列表失败');
  }
}

// 获取订单详细信息
export async function getOrderDetails(orderId: string) {
  try {
    // 验证管理员权限
    const session = await auth();
    if (!session?.user?.id) {
      throw new Error('未授权访问');
    }

    // 查询当前用户的管理员状态
    const currentUser = await db
      .select({ isAdmin: users.isAdmin })
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    if (!currentUser.length || !currentUser[0].isAdmin) {
      throw new Error('需要管理员权限');
    }

    // 查询订单详细信息
    const orderDetails = await db
      .select({
        id: purchaseOrders.id,
        workspaceId: purchaseOrders.workspaceId,
        workspaceName: workspaces.name,
        userId: purchaseOrders.userId,
        userEmail: users.email,
        userName: users.name,
        packageType: purchaseOrders.packageType,
        stripeSessionId: purchaseOrders.stripeSessionId,
        status: purchaseOrders.status,
        amountPaidCents: purchaseOrders.amountPaidCents,
        currency: purchaseOrders.currency,
        creditsPurchased: purchaseOrders.creditsPurchased,
        completedAt: purchaseOrders.completedAt,
        createdAt: purchaseOrders.createdAt,
        updatedAt: purchaseOrders.updatedAt
      })
      .from(purchaseOrders)
      .leftJoin(users, eq(purchaseOrders.userId, users.id))
      .leftJoin(workspaces, eq(purchaseOrders.workspaceId, workspaces.id))
      .where(eq(purchaseOrders.id, orderId))
      .limit(1);

    if (!orderDetails.length) {
      throw new Error('订单不存在');
    }

    const order = orderDetails[0];
    return {
      ...order,
      creditsPurchased: Number(order.creditsPurchased)
    };
  } catch (error) {
    console.error('获取订单详情失败:', error);
    throw new Error('获取订单详情失败');
  }
}
'use client';

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Typography, 
  Table, 
  Tag, 
  Space, 
  Pagination, 
  message,
  Modal,
  Descriptions,
  Button
} from 'antd';
import { 
  CreditCardOutlined, 
  EyeOutlined,
  ShopOutlined,
  UserOutlined,
  DollarOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { getOrderList, getOrderDetails } from './actions';

const { Title, Paragraph } = Typography;

interface OrderInfo {
  id: string;
  workspaceId: string;
  workspaceName: string;
  userId: string;
  userEmail: string | null;
  packageType: string;
  stripeSessionId: string;
  status: string;
  amountPaidCents: number;
  currency: string;
  creditsPurchased: number;
  completedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export default function BillingsManagementPage() {
  const [orders, setOrders] = useState<OrderInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [pageSize] = useState(20);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);

  const loadOrders = async (page: number = currentPage) => {
    try {
      setLoading(true);
      const result = await getOrderList({
        page,
        limit: pageSize
      });
      
      setOrders(result.orders);
      setTotal(result.total);
      setCurrentPage(page);
    } catch (error: any) {
      message.error(error.message || '加载订单列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadOrders(1);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    loadOrders(page);
  };

  const handleViewDetails = async (orderId: string) => {
    try {
      setLoading(true);
      const orderDetails = await getOrderDetails(orderId);
      setSelectedOrder(orderDetails);
      setIsDetailModalVisible(true);
    } catch (error: any) {
      message.error(error.message || '获取订单详情失败');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'green';
      case 'pending':
        return 'orange';
      case 'failed':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'pending':
        return '待处理';
      case 'failed':
        return '失败';
      default:
        return status;
    }
  };

  const getPackageText = (packageType: string) => {
    switch (packageType) {
      case 'product_3':
        return '基础包 (3万积分)';
      case 'product_10':
        return '标准包 (10万积分)';
      case 'product_100':
        return '企业包 (100万积分)';
      default:
        return packageType;
    }
  };

  const formatAmount = (amountCents: number, currency: string) => {
    const amount = amountCents / 100;
    return `${currency.toUpperCase()} ${amount.toFixed(2)}`;
  };

  const formatNumber = (num: number) => {
    return num.toLocaleString();
  };

  const columns: ColumnsType<OrderInfo> = [
    {
      title: '订单ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
      render: (id: string) => (
        <code className="text-sm bg-gray-100 px-2 py-1 rounded">
          {id.slice(0, 8)}...
        </code>
      ),
    },
    {
      title: '用户信息',
      key: 'userInfo',
      render: (_, record) => (
        <div>
          <div className="flex items-center gap-1 text-sm">
            <UserOutlined className="text-blue-500" />
            <span>{record.userEmail || '未知用户'}</span>
          </div>
          <div className="text-xs text-gray-500">
            ID: {record.userId.slice(0, 8)}...
          </div>
        </div>
      ),
    },
    {
      title: '工作空间',
      key: 'workspace',
      render: (_, record) => (
        <div>
          <div className="flex items-center gap-1 text-sm">
            <ShopOutlined className="text-green-500" />
            <span>{record.workspaceName}</span>
          </div>
          <div className="text-xs text-gray-500">
            ID: {record.workspaceId}
          </div>
        </div>
      ),
    },
    {
      title: '套餐类型',
      dataIndex: 'packageType',
      key: 'packageType',
      width: 160,
      render: (packageType: string) => (
        <Tag color="blue">{getPackageText(packageType)}</Tag>
      ),
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '支付金额',
      key: 'amount',
      width: 120,
      render: (_, record) => (
        <div className="flex items-center gap-1">
          <DollarOutlined className="text-green-500" />
          <span className="font-mono">
            {formatAmount(record.amountPaidCents, record.currency)}
          </span>
        </div>
      ),
    },
    {
      title: '获得积分',
      dataIndex: 'creditsPurchased',
      key: 'creditsPurchased',
      width: 120,
      render: (credits: number) => (
        <div className="flex items-center gap-1">
          <CreditCardOutlined className="text-orange-500" />
          <span className="font-mono">{formatNumber(credits)}</span>
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (createdAt: Date) => 
        new Date(createdAt).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        }),
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (_, record) => (
        <Space>
          <Button
            icon={<EyeOutlined />}
            size="small"
            onClick={() => handleViewDetails(record.id)}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="w-full">
      <div className="mb-6">
        <Title level={2} className="flex items-center gap-2">
          <CreditCardOutlined />
          订单管理
        </Title>
        <Paragraph className="text-gray-600">
          管理系统中的所有订单和支付记录
        </Paragraph>
      </div>

      <Card className="w-full">
        <div className="mb-4 flex justify-between items-center">
          <div className="text-sm text-gray-500">
            共 {total} 个订单
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={orders}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={{ x: 1200 }}
          size="middle"
        />

        {total > pageSize && (
          <div className="mt-4 flex justify-center">
            <Pagination
              current={currentPage}
              total={total}
              pageSize={pageSize}
              onChange={handlePageChange}
              showSizeChanger={false}
              showQuickJumper
              showTotal={(total, range) => 
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }
            />
          </div>
        )}
      </Card>

      {/* 订单详情模态框 */}
      <Modal
        title="订单详情"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedOrder && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="订单ID" span={2}>
              <code className="text-sm">{selectedOrder.id}</code>
            </Descriptions.Item>
            <Descriptions.Item label="Stripe会话ID" span={2}>
              <code className="text-xs">{selectedOrder.stripeSessionId}</code>
            </Descriptions.Item>
            <Descriptions.Item label="用户邮箱">
              {selectedOrder.userEmail || '未知'}
            </Descriptions.Item>
            <Descriptions.Item label="用户姓名">
              {selectedOrder.userName || '未设置'}
            </Descriptions.Item>
            <Descriptions.Item label="用户ID">
              <code className="text-sm">{selectedOrder.userId}</code>
            </Descriptions.Item>
            <Descriptions.Item label="工作空间">
              {selectedOrder.workspaceName || '未知工作空间'}
            </Descriptions.Item>
            <Descriptions.Item label="工作空间ID">
              <code className="text-sm">{selectedOrder.workspaceId}</code>
            </Descriptions.Item>
            <Descriptions.Item label="套餐类型">
              <Tag color="blue">{getPackageText(selectedOrder.packageType)}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label="订单状态">
              <Tag color={getStatusColor(selectedOrder.status)}>
                {getStatusText(selectedOrder.status)}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="支付金额">
              <div className="flex items-center gap-1">
                <DollarOutlined className="text-green-500" />
                <span className="font-mono text-lg">
                  {formatAmount(selectedOrder.amountPaidCents, selectedOrder.currency)}
                </span>
              </div>
            </Descriptions.Item>
            <Descriptions.Item label="获得积分">
              <div className="flex items-center gap-1">
                <CreditCardOutlined className="text-orange-500" />
                <span className="font-mono text-lg">{formatNumber(selectedOrder.creditsPurchased)}</span>
              </div>
            </Descriptions.Item>
            <Descriptions.Item label="完成时间">
              {selectedOrder.completedAt 
                ? new Date(selectedOrder.completedAt).toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                  })
                : '未完成'
              }
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {selectedOrder.createdAt 
                ? new Date(selectedOrder.createdAt).toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                  })
                : '-'
              }
            </Descriptions.Item>
            <Descriptions.Item label="最后更新">
              {selectedOrder.updatedAt 
                ? new Date(selectedOrder.updatedAt).toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                  })
                : '-'
              }
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
}
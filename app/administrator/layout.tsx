"use client";
import React from 'react';
import Image from "next/image";
import Link from 'next/link';
import { Popconfirm, But<PERSON> } from 'antd';
import { LogoutOutlined, UserOutlined, CreditCardOutlined, ShopOutlined, GiftOutlined } from '@ant-design/icons';
import clsx from 'clsx';
import { usePathname } from 'next/navigation';
import logo from "@/app/images/logo.png";
import ToggleSidebar from "@/app/images/hideSidebar.svg";
import { useSession, signOut } from 'next-auth/react';
import SpinLoading from '@/app/components/loading/SpinLoading';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function AdministratorLayout({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <AdministratorRouteGuard>
      <AdministratorLayoutContent>{children}</AdministratorLayoutContent>
    </AdministratorRouteGuard>
  );
}

function AdministratorRouteGuard({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(true);
  const [hasPermission, setHasPermission] = useState(false);

  useEffect(() => {
    async function checkGlobalAdminPermission() {
      if (status === 'loading') {
        return;
      }

      if (!session?.user?.id) {
        router.replace('/404');
        return;
      }

      try {
        setIsChecking(true);
        // 检查用户是否为全局管理员
        const response = await fetch('/api/admin/check', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const { isAdmin } = await response.json();
          if (isAdmin) {
            setHasPermission(true);
          } else {
            router.replace('/404');
            return;
          }
        } else {
          router.replace('/404');
          return;
        }
      } catch (error) {
        console.error('Global admin permission check error:', error);
        router.replace('/404');
        return;
      } finally {
        setIsChecking(false);
      }
    }

    checkGlobalAdminPermission();
  }, [session, status, router]);

  if (status === 'loading' || isChecking) {
    return (
      <main className="h-dvh flex justify-center items-center">
        <SpinLoading />
        <span className='ml-2 text-gray-600'>Loading ...</span>
      </main>
    );
  }

  if (!hasPermission) {
    return null;
  }

  return <>{children}</>;
}

function AdministratorLayoutContent({
  children
}: Readonly<{
  children: React.ReactNode;
}>) {
  const pathname = usePathname();
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  const navigationItems = [
    {
      path: '/administrator/users',
      label: '用户管理',
      icon: <UserOutlined style={{ marginLeft: '3px' }} />
    },
    {
      path: '/administrator/billings',
      label: '订单管理',
      icon: <CreditCardOutlined style={{ marginLeft: '3px' }} />
    },
    {
      path: '/administrator/workspaces',
      label: '工作空间管理',
      icon: <ShopOutlined style={{ marginLeft: '3px' }} />
    },
    {
      path: '/administrator/redemption-code',
      label: '兑换码管理',
      icon: <GiftOutlined style={{ marginLeft: '3px' }} />
    }
  ];

  return (
    <div className="flex flex-row min-h-screen h-dvh">
      {/* 移动端遮罩层 */}
      {!isSidebarCollapsed && (
        <div
          className="md:hidden fixed inset-0 bg-black/30 z-40"
          onClick={() => setIsSidebarCollapsed(true)}
        />
      )}
      
      {/* 左侧导航栏 */}
      <div className={clsx(
        "flex flex-col w-64 bg-gray-100 h-dvh p-4 box-border transition-transform duration-300 ease-in-out z-50",
        "fixed md:relative",
        isSidebarCollapsed ? "md:-translate-x-full -translate-x-64" : ""
      )}>
        {/* 头部 */}
        <div className="flex items-center flex-row flex-grow-0 mb-2 h-10 justify-between">
          <div className='flex items-center'>
            <Image src={logo} className="ml-1" alt="HiveChat logo" width={24} height={24} />
            <span className='text-xl ml-2'>全局管理后台</span>
          </div>
          {/* <Button
            icon={<ToggleSidebar style={{ 'color': '#999', 'fontSize': '20px', 'verticalAlign': 'middle' }} />}
            type='text'
            onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
          /> */}
        </div>
        
        <hr className='mb-4' />
        
        {/* 导航菜单 */}
        <div className="flex-1">
          {navigationItems.map((item) => (
            <div 
              key={item.path}
              className={clsx(
                'hover:bg-gray-200 rounded-lg p-2 mt-1',
                { 'bg-gray-200': pathname.startsWith(item.path) }
              )}
            >
              <Link className='w-full flex items-center' href={item.path}>
                {item.icon}
                <span className='ml-2 text-sm'>{item.label}</span>
              </Link>
            </div>
          ))}
        </div>
        
        {/* 底部操作 */}
        <div className='mt-auto'>
          <div className="flex items-center flex-grow-0 h-10 border-gray-200">
            <Popconfirm
              title="确认退出登录？"
              description="退出登录后需要重新登录才能访问"
              onConfirm={() => {
                signOut({
                  redirect: true,
                  redirectTo: `/`
                });
              }}
              okText="确认"
              cancelText="取消"
            >
              <div className="flex items-center cursor-pointer text-sm pl-3 mt-2 hover:bg-gray-200 h-9 w-full rounded">
                <LogoutOutlined />
                <span className='ml-2'>退出登录</span>
              </div>
            </Popconfirm>
          </div>
        </div>
      </div>
      
      {/* 右侧内容区域 */}
      <div className={clsx(
        'flex flex-row grow mx-auto justify-center overflow-auto transition-all duration-300 ease-in-out h-dvh',
        isSidebarCollapsed ? 'md:-ml-64' : 'md:ml-0'
      )}>
        <div className="w-full p-6">
          {children}
        </div>
      </div>
    </div>
  );
}
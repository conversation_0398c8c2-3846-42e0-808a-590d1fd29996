import {
  boolean,
  timestamp,
  pgTable,
  pgEnum,
  text,
  primaryKey,
  integer,
  varchar,
  json,
  date,
  bigint,
  index,
  uuid,
  unique,
  foreignKey
} from "drizzle-orm/pg-core";
import type { AdapterAccountType } from "next-auth/adapters";
import { customAlphabet } from 'nanoid';
import { apiStyle, MCPToolResponse, MessageContent } from '@/types/llm'
import { WebSearchResponse } from '@/types/search'
const nanoid = customAlphabet('1234567890abcdefghijklmnopqrstuvwxyz', 10)

export const users = pgTable("user", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => crypto.randomUUID()),
  name: text("name"),
  email: text("email").unique(),
  password: text("password"),
  dingdingUnionId: text("dingdingUnionId"),
  wecomUserId: text("wecomUserId"),
  feishuUserId: text("feishuUserId"),
  feishuOpenId: text("feishuOpenId"),
  feishuUnionId: text("feishuUnionId"),
  emailVerified: timestamp("emailVerified", { mode: "date" }),
  isAdmin: boolean("isAdmin").default(false),
  image: text("image"),
  createdAt: timestamp('created_at').defaultNow(),
})

export const accounts = pgTable("account", {
  userId: text("userId")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  type: text("type").$type<AdapterAccountType>().notNull(),
  provider: text("provider").notNull(),
  providerAccountId: text("providerAccountId").notNull(),
  refresh_token: text("refresh_token"),
  access_token: text("access_token"),
  expires_at: integer("expires_at"),
  token_type: text("token_type"),
  scope: text("scope"),
  id_token: text("id_token"),
  session_state: text("session_state"),
},
  (account) => [
    {
      compoundKey: primaryKey({
        columns: [account.provider, account.providerAccountId],
      }),
    },
  ]
)

export const sessions = pgTable("session", {
  sessionToken: text("sessionToken").primaryKey(),
  userId: text("userId")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  expires: timestamp("expires", { mode: "date" }).notNull(),
})

export const verificationTokens = pgTable("verificationToken", {
  identifier: text("identifier").notNull(),
  token: text("token").notNull(),
  expires: timestamp("expires", { mode: "date" }).notNull(),
},
  (verificationToken) => [
    {
      compositePk: primaryKey({
        columns: [verificationToken.identifier, verificationToken.token],
      }),
    },
  ]
)

export const authenticators = pgTable("authenticator", {
  credentialID: text("credentialID").notNull().unique(),
  userId: text("userId")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  providerAccountId: text("providerAccountId").notNull(),
  credentialPublicKey: text("credentialPublicKey").notNull(),
  counter: integer("counter").notNull(),
  credentialDeviceType: text("credentialDeviceType").notNull(),
  credentialBackedUp: boolean("credentialBackedUp").notNull(),
  transports: text("transports"),
},
  (authenticator) => [
    {
      compositePK: primaryKey({
        columns: [authenticator.userId, authenticator.credentialID],
      }),
    },
  ]
)

export const APIStyle = pgEnum('api_style', ['openai', 'openai_response', 'claude', 'gemini']);
export const providerType = pgEnum('provider_type', ['default', 'custom']);
export const llmSettingsTable = pgTable("llm_settings", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  provider: varchar({ length: 255 }).notNull(),
  providerName: varchar({ length: 255 }).notNull(),
  apikey: varchar({ length: 255 }),
  endpoint: varchar({ length: 1024 }),
  isActive: boolean('is_active').default(false),
  apiStyle: APIStyle('api_style').notNull().default('openai'),
  type: providerType('type').notNull().default('default'),
  logo: varchar({ length: 2048 }),
  order: integer('order'),
  workspaceId: varchar("workspace_id", { length: 255 }).notNull().references(() => workspaces.id, { onDelete: "cascade" }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
}, (table) => [
  // 创建联合唯一约束，确保同一个 workspace 内 provider 唯一
  unique('unique_provider_workspace').on(
    table.provider,
    table.workspaceId
  ),
]);

// 全局应用设置表（系统级别设置）
export const globalAppSettings = pgTable("global_app_settings", {
  key: text("key").primaryKey(),
  value: text('value'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
});

// 工作空间级别应用设置表
export const appSettings = pgTable("app_settings", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  key: text("key").notNull(),
  value: text('value'),
  workspaceId: varchar("workspace_id", { length: 255 }).notNull().references(() => workspaces.id, { onDelete: "cascade" }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow()
}, (table) => [
  // 创建联合唯一约束，确保同一个 workspace 内 key 唯一
  unique('unique_key_workspace').on(
    table.key,
    table.workspaceId
  ),
]);

export const modelType = pgEnum('model_type', ['default', 'custom']);

export const llmModels = pgTable("models", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  name: varchar({ length: 255 }).notNull(),
  displayName: varchar({ length: 255 }).notNull(),
  maxTokens: integer(),
  supportVision: boolean('support_vision').default(false),
  supportTool: boolean('support_tool').default(false),
  builtInImageGen: boolean('built_in_image_gen').default(false),
  builtInWebSearch: boolean('built_in_web_search').default(false),
  selected: boolean('selected').default(true),
  providerId: varchar({ length: 255 }).notNull(), // 移除单独的外键引用
  providerName: varchar({ length: 255 }).notNull(),
  type: modelType('type').notNull().default('default'),
  logo: varchar({ length: 2048 }),
  order: integer('order').default(1),
  workspaceId: varchar("workspace_id", { length: 255 }).notNull().references(() => workspaces.id, { onDelete: "cascade" }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
}, (table) => [
  // 联合外键约束：(providerId, workspaceId) -> (provider, workspaceId)
  foreignKey({
    columns: [table.providerId, table.workspaceId],
    foreignColumns: [llmSettingsTable.provider, llmSettingsTable.workspaceId],
    name: "models_provider_workspace_fk"
  }).onDelete('cascade').onUpdate('cascade'),

  // 创建联合唯一约束
  unique('unique_model_provider').on(
    table.name,
    table.providerId,
    table.workspaceId
  ),
]);

export const avatarType = pgEnum('avatar_type', ['emoji', 'url', 'none']);
export const historyType = pgEnum('history_type', ['all', 'count', 'none']);

export const chats = pgTable("chats", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => nanoid()),
  userId: text(),
  title: varchar({ length: 255 }).notNull(),
  historyType: historyType('history_type').notNull().default('count'),
  historyCount: integer('history_count').default(5).notNull(),
  searchEnabled: boolean('search_enabled').default(false),
  defaultModel: varchar('default_model'),
  defaultProvider: varchar('default_provider'),
  isStar: boolean('is_star').default(false),
  isWithBot: boolean('is_with_bot').default(false),
  botId: integer('bot_id'),
  avatar: varchar('avatar'),
  avatarType: avatarType('avatar_type').notNull().default('none'),
  prompt: text(),
  starAt: timestamp('star_at'),
  inputTokens: integer('input_tokens').notNull().default(0),
  outputTokens: integer('output_tokens').notNull().default(0),
  totalTokens: integer('total_tokens').notNull().default(0),
  workspaceId: varchar("workspace_id", { length: 255 }).notNull().references(() => workspaces.id, { onDelete: "cascade" }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const messageType = pgEnum('message_type', ['text', 'image', 'error', 'break']);
export const messageSearchStatus = pgEnum('search_status', ['none', 'searching', 'error', 'done']);

export const messages = pgTable("messages", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  userId: text().notNull(),
  chatId: text().notNull(),
  workspaceId: varchar("workspace_id", { length: 255 }).notNull().references(() => workspaces.id, { onDelete: "cascade" }),
  role: varchar({ length: 255 }).notNull(),
  content: json('content').$type<MessageContent>(),
  reasoninContent: text('reasonin_content'),
  model: varchar({ length: 255 }),
  providerId: varchar({ length: 255 }).notNull(),
  type: varchar('message_type').notNull().default('text'),
  searchEnabled: boolean('search_enabled').default(false),
  webSearch: json('web_search').$type<WebSearchResponse>(),
  searchStatus: messageSearchStatus('search_status').notNull().default('none'),
  mcpTools: json('mcp_tools').$type<MCPToolResponse[]>(),
  inputTokens: integer('input_tokens'),
  outputTokens: integer('output_tokens'),
  totalTokens: integer('total_tokens'),
  creditsDeducted: bigint('credits_deducted', { mode: 'number' }),
  errorType: varchar('error_type'),
  errorMessage: varchar('error_message'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
  deleteAt: timestamp('delete_at'),
});

export const bots = pgTable("bots", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  title: varchar({ length: 255 }).notNull(),
  desc: text('desc'),
  prompt: text('prompt'),
  avatarType: avatarType('avatar_type').notNull().default('none'),
  avatar: varchar('avatar'),
  sourceUrl: varchar('source_url'),
  creator: varchar(),
  workspaceId: varchar("workspace_id", { length: 255 }).notNull().references(() => workspaces.id, { onDelete: "cascade" }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
  deleteAt: timestamp('delete_at'),
});

export interface BotType {
  id?: number;
  title: string;
  desc?: string;
  prompt: string;
  avatar: string;
  avatarType: 'emoji' | 'url';
  sourceUrl?: string;
  creator: string;
  createdAt: Date;
}

export type UserType = typeof users.$inferSelect;

export type llmModelType = typeof llmModels.$inferSelect & {
  providerLogo?: string;
  apiStyle: apiStyle;
};

export type llmSettingsType = typeof llmSettingsTable.$inferSelect;

export const groupModelType = pgEnum('group_model_type', ['all', 'specific'])
export const tokenLimitType = pgEnum('token_limit_type', ['unlimited', 'limited'])

export const groups = pgTable("groups", {
  id: text("id").primaryKey().$defaultFn(() => crypto.randomUUID()),
  name: text("name").notNull(),
  modelType: groupModelType('model_type').notNull().default('all'),
  tokenLimitType: tokenLimitType('token_limit_type').notNull().default('unlimited'),
  monthlyTokenLimit: integer('monthly_token_limit'),
  isDefault: boolean("is_default").default(false),
  workspaceId: varchar("workspace_id", { length: 255 }).notNull().references(() => workspaces.id, { onDelete: "cascade" }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
})

export const usageReport = pgTable("usage_report", {
  date: date("date").notNull(),
  userId: text("user_id"),
  modelId: varchar('model_id', { length: 255 }),
  providerId: varchar("provider_id", { length: 255 }),
  inputTokens: integer('input_tokens').notNull().default(0),
  outputTokens: integer('output_tokens').notNull().default(0),
  totalTokens: integer('total_tokens').notNull().default(0),
  workspaceId: varchar("workspace_id", { length: 255 }).notNull().references(() => workspaces.id, { onDelete: "cascade" }),
},
  (usageReport) => [
    {
      compositePK: primaryKey({
        columns: [usageReport.date, usageReport.userId, usageReport.modelId, usageReport.providerId, usageReport.workspaceId],
      }),
    },
  ])

export const creditUsageReport = pgTable("credit_usage_report", {
  date: date("date").notNull(),
  userId: text("user_id"),
  modelId: varchar('model_id', { length: 255 }),
  providerId: varchar("provider_id", { length: 255 }),
  inputTokens: integer('input_tokens').notNull().default(0),
  outputTokens: integer('output_tokens').notNull().default(0),
  totalTokens: integer('total_tokens').notNull().default(0),
  inputCredits: integer('input_tokens').notNull().default(0),
  outputCredits: integer('output_tokens').notNull().default(0),
  totalCredits: integer('total_tokens').notNull().default(0),
  workspaceId: varchar("workspace_id", { length: 255 }).notNull().references(() => workspaces.id, { onDelete: "cascade" }),
},
  (creditUsageReport) => [
    {
      compositePK: primaryKey({
        columns: [creditUsageReport.date, creditUsageReport.userId, creditUsageReport.modelId, creditUsageReport.providerId, creditUsageReport.workspaceId],
      }),
    },
  ])

export const searchEngineConfig = pgTable("search_engine_config", {
  id: text("id").notNull(),
  name: text("name").notNull(),
  apiKey: text("api_key"),
  maxResults: integer("max_results").default(5).notNull(),
  extractKeywords: boolean("extract_keywords").default(false).notNull(),
  workspaceId: varchar("workspace_id", { length: 255 }).notNull().references(() => workspaces.id, { onDelete: "cascade" }),
  isActive: boolean("is_active").default(false).notNull(),
}, (table) => [
  // 联合主键：(id, workspaceId)
  primaryKey({ columns: [table.id, table.workspaceId] }),
])

export const mcpServerType = pgEnum('mcp_server_type', ['sse', 'streamableHttp'])
export const mcpServers = pgTable("mcp_servers", {
  id: uuid("id").primaryKey().$defaultFn(() => crypto.randomUUID()),
  name: text("name").notNull(),
  description: text("description"),
  type: mcpServerType('type').default('sse'),
  baseUrl: text("base_url").notNull(),
  workspaceId: varchar("workspace_id", { length: 255 }).notNull().references(() => workspaces.id, { onDelete: "cascade" }),
  isActive: boolean("is_active").default(false).notNull(),
  createdAt: timestamp('created_at').defaultNow(),
}, (table) => [
  // 在同一个 workspace 内，name 应该是唯一的
  unique('unique_mcp_server_name_workspace').on(
    table.name,
    table.workspaceId
  ),
])

export const mcpTools = pgTable("mcp_tools", {
  id: uuid("id").primaryKey().$defaultFn(() => crypto.randomUUID()),
  name: text("name").notNull(),
  serverId: uuid("server_id")
    .notNull()
    .references(() => mcpServers.id, { onDelete: "cascade" }),
  description: text("description"),
  inputSchema: text('input_schema').notNull(),
})

export const groupModels = pgTable("group_models", {
  groupId: text("groupId").notNull().references(() => groups.id, { onDelete: 'cascade' }),
  modelId: integer("modelId").notNull().references(() => llmModels.id, { onDelete: 'cascade' }),
},
  (groupModels) => [
    {
      compositePK: primaryKey({
        columns: [groupModels.groupId, groupModels.modelId],
      }),
    }
  ]
)

export const planType = pgEnum('plan_type', ['free', 'plus', 'pro'])
export const workspaces = pgTable("workspaces", {
  id: varchar({ length: 255 }).notNull().primaryKey(),
  name: varchar({ length: 255 }).notNull(),
  owner: text(),  //创建者用户 id
  plan: planType('plan').notNull().default('free'), // 预留的套餐类型
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
})

export const userRoleType = pgEnum('user_role_type', ['owner', 'admin', 'member']);

export const userWorkspace = pgTable("user_workspace", {
  id: text("id").primaryKey().$defaultFn(() => crypto.randomUUID()),
  userId: text("user_id").references(() => users.id, { onDelete: "cascade" }),
  workspaceId: varchar("workspace_id", { length: 255 }).notNull().references(() => workspaces.id, { onDelete: "cascade" }),
  email: text("email"), // 用于未注册用户的邮箱标识
  role: userRoleType('role').notNull().default('member'),
  isActive: boolean("is_active").default(true),
  joinedAt: timestamp('joined_at').defaultNow(),
  groupId: text("groupId"),
  todayTotalTokens: integer('today_total_tokens').notNull().default(0),
  currentMonthTotalTokens: integer('current_month_total_tokens').notNull().default(0),
  usageUpdatedAt: timestamp('usage_updated_at').notNull().defaultNow(),
  creditBalance: bigint('credit_balance', { mode: 'number' }).notNull().default(0), //积分余额
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
})

/**
 * Purchase Orders (购买订单表)
 * 1. 记录用户的每一次充值行为。
 */
export const packageType = pgEnum('package_type', ['product_3', 'product_10', 'product_100']);
export const orderStatusEnum = pgEnum('order_status', ['pending', 'completed', 'failed']);
export const transactionTypeEnum = pgEnum('transaction_type', ['purchase', 'consumption', 'refund', 'gift', 'redemption_code']);

export const purchaseOrders = pgTable('purchase_orders', {
  id: uuid('id').primaryKey().defaultRandom(),
  workspaceId: varchar("workspace_id", { length: 255 }).notNull().references(() => workspaces.id, { onDelete: 'cascade' }),
  userId: text("user_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  packageType: packageType('package_type').notNull(),
  stripeSessionId: varchar('stripe_session_id', { length: 255 }).notNull().unique(),
  status: orderStatusEnum('status').notNull().default('pending'),
  amountPaidCents: integer('amount_paid_cents').notNull(),
  currency: varchar('currency', { length: 10 }).notNull(),
  creditsPurchased: bigint('credits_purchased', { mode: 'number' }).notNull(),
  completedAt: timestamp('completed_at', { withTimezone: true }),
  createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
});

/**
 * 3. Credit Transactions (积分流水表) - **核心审计表**
 * 记录每一次积分变动，提供完整的、不可变的财务审计日志。credit_transactions_workspace_id_workspaces_id_fk
 */
export const creditTransactions = pgTable('credit_transactions', {
  id: uuid('id').primaryKey().defaultRandom(),
  workspaceId: varchar("workspace_id", { length: 255 }).notNull().references(() => workspaces.id, { onDelete: 'cascade' }),
  userId: text("user_id").notNull().references(() => users.id, { onDelete: 'cascade' }),
  transactionType: transactionTypeEnum('transaction_type').notNull(),
  inputCredits: bigint('input_credits', { mode: 'number' }),
  outputCredits: bigint('output_credits', { mode: 'number' }),
  totalCredits: bigint('total_credits', { mode: 'number' }).notNull(), // 正数增加, 负数减少
  balanceAfter: bigint('balance_after', { mode: 'number' }).notNull(),
  modelId: text('model_id'),
  // 多态关联的实现方式：记录关联的ID和类型
  relatedEntityId: text('related_entity_id').notNull(),
  relatedEntityType: text('related_entity_type').notNull(), // e.g., 'PurchaseOrder' or 'MessageId'
  notes: text('notes'),
  createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
}, (table) => [
  // 核心查询索引
  index('transaction_user_idx').on(table.userId),
  index('transaction_workspace_idx').on(table.workspaceId),
]);

/**
 * Webhook Events (Webhook 事件记录表)
 * 用于记录已处理的 webhook 事件，实现幂等性，避免重复处理同一事件
 */
export const webhookEventStatusEnum = pgEnum('webhook_event_status', ['pending', 'processed', 'failed']);

export const webhookEvents = pgTable('webhook_events', {
  id: uuid('id').primaryKey().defaultRandom(),
  eventId: varchar('event_id', { length: 255 }).notNull().unique(), // Stripe event ID
  eventType: varchar('event_type', { length: 100 }).notNull(), // e.g., 'checkout.session.completed'
  status: webhookEventStatusEnum('status').notNull().default('pending'),
  stripeSessionId: varchar('stripe_session_id', { length: 255 }), // 关联的 Stripe session ID
  orderId: uuid('order_id').references(() => purchaseOrders.id), // 关联的订单ID
  processedAt: timestamp('processed_at', { withTimezone: true }),
  errorMessage: text('error_message'),
  retryCount: integer('retry_count').notNull().default(0),
  rawEventData: json('raw_event_data'), // 存储原始事件数据用于调试
  createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow(),
}, (table) => [
  // 索引
  index('webhook_event_id_idx').on(table.eventId),
  index('webhook_session_id_idx').on(table.stripeSessionId),
  index('webhook_status_idx').on(table.status),
  index('webhook_created_at_idx').on(table.createdAt),
]);

// 工作空间邀请码表
export const workspaceInvites = pgTable("workspace_invites", {
  id: uuid("id").primaryKey().$defaultFn(() => crypto.randomUUID()),
  workspaceId: varchar("workspace_id", { length: 255 }).notNull().references(() => workspaces.id, { onDelete: "cascade" }),
  inviteCode: varchar("invite_code", { length: 255 }).notNull().unique(),
  createdBy: text("created_by").notNull().references(() => users.id, { onDelete: "cascade" }),
  expiresAt: timestamp("expires_at").notNull(),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
}, (table) => [
  // 邀请码全局唯一
  unique('unique_invite_code').on(table.inviteCode),
])

// 兑换码表
export const redemptionCodes = pgTable("redemption_codes", {
  id: uuid("id").primaryKey().$defaultFn(() => crypto.randomUUID()),
  code: varchar("code", { length: 8 }).notNull().unique(), // 8位随机字母数字组合
  expiresAt: timestamp("expires_at").notNull(), // 有效期截止时间
  usageLimit: integer("usage_limit").notNull().default(1), // 使用次数限制
  usedCount: integer("used_count").notNull().default(0), // 已使用次数
  creditAmount: bigint('credit_amount', { mode: 'number' }).notNull(), // 获取积分数
  isActive: boolean("is_active").default(true), // 是否激活
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
}, (table) => [
  // 兑换码全局唯一
  unique('unique_redemption_code').on(table.code),
])

// 兑换码使用记录表
export const redemptionCodeUsages = pgTable("redemption_code_usages", {
  id: uuid("id").primaryKey().$defaultFn(() => crypto.randomUUID()),
  userId: text("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  redemptionCodeId: uuid("redemption_code_id").notNull().references(() => redemptionCodes.id, { onDelete: "cascade" }),
  code: varchar("code", { length: 8 }).notNull(), // 兑换码的值（冗余存储便于查询）
  workspaceId: varchar("workspace_id", { length: 255 }).notNull().references(() => workspaces.id, { onDelete: "cascade" }),
  creditAmount: bigint('credit_amount', { mode: 'number' }).notNull(), // 实际获得的积分数
  usedAt: timestamp("used_at").defaultNow(), // 使用时间
}, (table) => [
  // 同一个用户在同一个工作空间中，同一个兑换码只能使用一次
  unique('unique_user_workspace_code').on(table.userId, table.workspaceId, table.redemptionCodeId),
])
import { LLMModel, LLMModelProvider } from "@/types/llm"
export const provider: LLMModelProvider = {
  id: 'groq',
  providerName: 'Groq',
  apiStyle: 'openai',
}

export const modelList: LLMModel[] = [
  {
    'id': 'moonshotai/kimi-k2-instruct',
    'displayName': 'Kimi K2',
    'supportVision': true,
    'supportTool': true,
    "maxTokens": 131072,
    'selected': true,
    provider
  },
  {
    'id': 'qwen/qwen3-32b',
    'displayName': 'Qwen 3',
    'supportVision': false,
    'supportTool': true,
    "maxTokens": 131072,
    'selected': true,
    provider
  },
  {
    'id': 'deepseek-r1-distill-llama-70b',
    'displayName': 'Deepseek R1',
    'supportVision': false,
    'supportTool': true,
    "maxTokens": 65536,
    'selected': true,
    provider
  },
]
import { LLMModel, LL<PERSON>odelProvider } from "@/types/llm"
export const provider: LLMModelProvider = {
  id: 'claude',
  providerName: '<PERSON>',
  apiStyle: 'claude',
}

export const modelList: LLMModel[] = [
  {
    'id': 'claude-sonnet-4-20250514',
    'displayName': 'Claude Sonnet 4 ',
    'supportVision': true,
    'supportTool': true,
    'maxTokens': 204800,
    'selected': true,
    provider
  },
  {
    'id': 'claude-opus-4-1-20250805',
    'displayName': 'Claude Opus 4.1',
    'supportVision': true,
    'supportTool': true,
    'maxTokens': 204800,
    'selected': true,
    provider
  },
  {
    'id': 'claude-opus-4-20250514',
    'displayName': 'Claude Opus 4',
    'supportVision': true,
    'supportTool': true,
    'maxTokens': 204800,
    'selected': true,
    provider
  },
  {
    'id': 'claude-3-7-sonnet-20250219',
    'displayName': 'Claude Sonnet 3.7',
    'supportVision': true,
    'supportTool': true,
    'maxTokens': 204800,
    'selected': true,
    provider
  },
  {
    'id': 'claude-3-5-haiku-20241022',
    'displayName': 'Claude Haiku 3.5',
    'supportVision': true,
    'supportTool': true,
    'maxTokens': 204800,
    'selected': true,
    provider
  }
]
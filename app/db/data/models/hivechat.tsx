import { LLMModel, LLMModelProvider } from "@/types/llm"
export const provider: LLMModelProvider = {
  id: 'hivechat',
  providerName: 'HiveChat',
  apiStyle: 'openai',
}

export const modelList: LLMModel[] = [
  {
    'id': 'openai/gpt-5-chat',
    'displayName': 'GPT 5',
    'supportVision': true,
    'supportTool': true,
    "maxTokens": 400 * 1024,
    'selected': true,
    'inputPriceCoefficient': 1.25,
    'outputPriceCoefficient': 10,
    'logo': '/images/providers/openai.svg',
    provider
  },
  {
    'id': 'openai/gpt-5-mini',
    'displayName': 'GPT 5 mini',
    'supportVision': true,
    'supportTool': true,
    "maxTokens": 400 * 1024,
    'selected': true,
    'inputPriceCoefficient': 0.25,
    'outputPriceCoefficient': 2,
    'logo': '/images/providers/openai.svg',
    provider
  },
  {
    'id': 'openai/gpt-5-nano',
    'displayName': 'GPT 5 nano',
    'supportVision': true,
    'supportTool': true,
    "maxTokens": 400 * 1024,
    'selected': true,
    'inputPriceCoefficient': 0.05,
    'outputPriceCoefficient': 0.4,
    'logo': '/images/providers/openai.svg',
    provider
  },
  {
    'id': 'openai/gpt-4.1',
    'displayName': 'GPT 4.1',
    'supportVision': true,
    'supportTool': true,
    "maxTokens": 1024 * 1024,
    'selected': false,
    'inputPriceCoefficient': 2,
    'outputPriceCoefficient': 8,
    'logo': '/images/providers/openai.svg',
    provider
  },
  {
    'id': 'openai/gpt-4.1-mini',
    'displayName': 'GPT 4.1 mini',
    'supportVision': true,
    'supportTool': true,
    "maxTokens": 1024 * 1024,
    'selected': false,
    'inputPriceCoefficient': 0.4,
    'outputPriceCoefficient': 1.6,
    'logo': '/images/providers/openai.svg',
    provider
  },
  {
    'id': 'openai/gpt-4.1-nano',
    'displayName': 'GPT 4.1 nano',
    "maxTokens": 1024 * 1024,
    'supportVision': true,
    'selected': false,
    'inputPriceCoefficient': 0.1,
    'outputPriceCoefficient': 0.4,
    'logo': '/images/providers/openai.svg',
    provider
  },
  {
    'id': 'openai/gpt-4o',
    'displayName': 'GPT 4o',
    "maxTokens": 128 * 1024,
    'supportVision': true,
    'selected': false,
    'inputPriceCoefficient': 2.5,
    'outputPriceCoefficient': 10,
    'logo': '/images/providers/openai.svg',
    provider
  },
  {
    'id': 'openai/gpt-4o-mini',
    'displayName': 'GPT 4o mini',
    "maxTokens": 128 * 1024,
    'supportVision': true,
    'selected': false,
    'inputPriceCoefficient': 0.15,
    'outputPriceCoefficient': 0.6,
    'logo': '/images/providers/openai.svg',
    provider
  },
  {
    'id': 'openai/o4-mini',
    'displayName': 'GPT o4 mini',
    "maxTokens": 200 * 1024,
    'supportVision': true,
    'selected': true,
    'inputPriceCoefficient': 1.1,
    'outputPriceCoefficient': 4.4,
    'logo': '/images/providers/openai.svg',
    provider
  },
  {
    'id': 'anthropic/claude-sonnet-4',
    'displayName': 'Claude Sonnet 4',
    'supportVision': true,
    'supportTool': true,
    "maxTokens": 200 * 1024,
    'selected': true,
    'inputPriceCoefficient': 3,
    'outputPriceCoefficient': 15,
    'logo': '/images/providers/claude.svg',
    provider
  },
  {
    'id': 'anthropic/claude-opus-4.1',
    'displayName': 'Claude Opus 4.1',
    'supportVision': true,
    'supportTool': true,
    "maxTokens": 200 * 1024,
    'selected': true,
    'inputPriceCoefficient': 15,
    'outputPriceCoefficient': 75,
    'logo': '/images/providers/claude.svg',
    provider
  },
  {
    'id': 'anthropic/claude-opus-4',
    'displayName': 'Claude Opus 4',
    'supportVision': true,
    'supportTool': true,
    "maxTokens": 200 * 1024,
    'selected': false,
    'inputPriceCoefficient': 15,
    'outputPriceCoefficient': 75,
    'logo': '/images/providers/claude.svg',
    provider
  },
  {
    'id': 'anthropic/claude-3.7-sonnet',
    'displayName': 'Claude Sonnet 3.7',
    'supportVision': true,
    'supportTool': true,
    "maxTokens": 200 * 1024,
    'selected': true,
    'inputPriceCoefficient': 3,
    'outputPriceCoefficient': 15,
    'logo': '/images/providers/claude.svg',
    provider
  },
  {
    'id': 'anthropic/claude-3.5-haiku',
    'displayName': 'Claude Haiku 3.5',
    'supportVision': false,
    'supportTool': true,
    "maxTokens": 200 * 1024,
    'selected': false,
    'inputPriceCoefficient': 1,
    'outputPriceCoefficient': 5,
    'logo': '/images/providers/claude.svg',
    provider
  },
  {
    'id': 'google/gemini-2.5-pro',
    'displayName': 'Gemini 2.5 Pro',
    'supportVision': true,
    'supportTool': true,
    "maxTokens": 1024 * 1024,
    'selected': true,
    'inputPriceCoefficient': 1.25,
    'outputPriceCoefficient': 10,
    'logo': '/images/providers/gemini.svg',
    provider
  },
  {
    'id': 'google/gemini-2.5-flash',
    'displayName': 'Gemini 2.5 Flash',
    'supportVision': true,
    'supportTool': true,
    "maxTokens": 1024 * 1024,
    'selected': true,
    'inputPriceCoefficient': 0.15,
    'outputPriceCoefficient': 3.45,
    'logo': '/images/providers/gemini.svg',
    provider
  },
  {
    'id': 'deepseek/deepseek-chat-v3-0324',
    'displayName': 'Deepseek V3',
    'supportVision': false,
    'supportTool': true,
    "maxTokens": 160 * 1024,
    'selected': true,
    'inputPriceCoefficient': 0.28,
    'outputPriceCoefficient': 1.1,
    'logo': '/images/providers/deepseek.svg',
    provider
  },
  {
    'id': 'deepseek/deepseek-r1-0528',
    'displayName': 'Deepseek R1',
    'supportVision': false,
    'supportTool': true,
    "maxTokens": 1024 * 1024,
    'selected': true,
    'inputPriceCoefficient': 0.55,
    'outputPriceCoefficient': 2.19,
    'logo': '/images/providers/deepseek.svg',
    provider
  },
  {
    'id': 'moonshotai/kimi-k2',
    'displayName': 'Kimi K2',
    'supportVision': false,
    'supportTool': true,
    "maxTokens": 128 * 1024,
    'selected': true,
    'inputPriceCoefficient': 0.6,
    'outputPriceCoefficient': 2.5,
    'logo': '/images/providers/moonshot.svg',
    provider
  },
]
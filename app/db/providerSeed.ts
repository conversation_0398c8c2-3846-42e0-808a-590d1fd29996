import 'dotenv/config';
import { llmSettingsTable, llmModels, workspaces } from '@/app/db/schema';
import { db } from './index';
import { eq, and } from 'drizzle-orm';
import { modelList as HivechatModels } from './data/models/hivechat';

export async function initializeHiveChatDefaultProvider() {
  try {
    console.log('开始执行 HiveChat provider 和 models 数据迁移...');
    
    // 查询所有工作空间
    const allWorkspaces = await db.select().from(workspaces);
    console.log(`找到 ${allWorkspaces.length} 个工作空间需要迁移`);
    
    if (allWorkspaces.length === 0) {
      console.log('没有找到工作空间，跳过迁移');
      return;
    }

    // HiveChat provider 配置
    const hivechatProviderConfig = {
      provider: 'hivechat',
      providerName: 'HiveChat',
      apikey: null,
      endpoint: null,
      isActive: true,
      apiStyle: 'openai' as const,
      type: 'default' as const,
      logo: '/images/providers/hivechat.svg',
      order: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    let providersCreated = 0;
    let modelsCreated = 0;

    // 使用事务处理每个工作空间
    await db.transaction(async (tx) => {
      for (const workspace of allWorkspaces) {
        const workspaceId = workspace.id;
        console.log(`处理工作空间: ${workspaceId}`);

        // 检查是否已存在 HiveChat provider
        const existingProvider = await tx.select()
          .from(llmSettingsTable)
          .where(
            and(
              eq(llmSettingsTable.provider, 'hivechat'),
              eq(llmSettingsTable.workspaceId, workspaceId)
            )
          );

        // 如果不存在 HiveChat provider，则创建
        if (existingProvider.length === 0) {
          await tx.insert(llmSettingsTable).values({
            ...hivechatProviderConfig,
            workspaceId
          });
          providersCreated++;
          console.log(`  ✅ 为工作空间 ${workspaceId} 创建了 HiveChat provider`);
        } else {
          console.log(`  ⏭️  工作空间 ${workspaceId} 已存在 HiveChat provider，跳过`);
        }

        // 检查并创建 HiveChat models
        for (const model of HivechatModels) {
          const existingModel = await tx.select()
            .from(llmModels)
            .where(
              and(
                eq(llmModels.name, model.id),
                eq(llmModels.providerId, 'hivechat'),
                eq(llmModels.workspaceId, workspaceId)
              )
            );

          // 如果模型不存在，则创建
          if (existingModel.length === 0) {
            await tx.insert(llmModels).values({
              name: model.id,
              displayName: model.displayName,
              maxTokens: model.maxTokens,
              supportVision: model.supportVision,
              supportTool: model.supportTool,
              builtInImageGen: model.builtInImageGen,
              builtInWebSearch: model.builtInWebSearch,
              selected: model.selected,
              providerId: 'hivechat',
              providerName: 'HiveChat',
              type: model.type ?? 'default',
              logo: model.logo,
              workspaceId,
              createdAt: new Date(),
              updatedAt: new Date(),
            });
            modelsCreated++;
            console.log(`  ✅ 为工作空间 ${workspaceId} 创建了模型: ${model.displayName}`);
          }
        }
      }
    });

    console.log(`✅ 数据迁移完成！`);
    console.log(`📊 统计信息:`);
    console.log(`   - 处理工作空间数: ${allWorkspaces.length}`);
    console.log(`   - 创建 HiveChat providers: ${providersCreated}`);
    console.log(`   - 创建 HiveChat models: ${modelsCreated}`);

  } catch (error) {
    console.error('❌ HiveChat 数据迁移失败:', error);
    throw error;
  }
}

initializeHiveChatDefaultProvider().then(() => {
  console.log("Providers initialized successfully.");
  process.exit(0); // 成功退出
}).catch((error) => {
  console.error("Error initializing providers:", error);
  process.exit(1);
});
import React from 'react';
import type { Metada<PERSON> } from "next";
import type { Viewport } from 'next'
import { AntdRegistry } from '@ant-design/nextjs-registry';
import { NextIntlClientProvider } from 'next-intl';
import { getLocale, getMessages } from 'next-intl/server';
import { SessionProvider } from 'next-auth/react';
import AmplitudeProvider from '@/app/components/AmplitudeProvider'
import "./globals.css";

export const metadata: Metadata = {
  title: "HiveChat - Chatbot for Team",
  description: "为中小团队设计的开源AI聊天应用，支持私有部署、多用户管理、权限控制、MCP协议，一站式接入OpenAI、Claude、Gemini等主流AI服务商，安全可靠的企业级AI解决方案",
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: 'cover',
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = await getLocale();
  const messages = await getMessages();
  return (
    <html lang={locale}>
      <body>
        <NextIntlClientProvider messages={messages}>
          <SessionProvider>
            <AntdRegistry>
              <AmplitudeProvider />
              {children}
            </AntdRegistry>
          </SessionProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}

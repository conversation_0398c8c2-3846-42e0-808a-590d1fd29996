import Link from 'next/link'
import { Button } from 'antd'
import { HomeOutlined } from '@ant-design/icons'

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full text-center">
        <div className="mb-8">
          <h1 className="text-6xl font-bold text-gray-900 mb-4">404</h1>
          <h2 className="text-2xl font-semibold text-gray-700 mb-2">页面未找到</h2>
          <p className="text-gray-500">
            抱歉，您访问的页面不存在或您没有权限访问该页面。
          </p>
        </div>
        
        <div className="space-y-4">
          <Link href="/">
            <Button type="primary" size="large" icon={<HomeOutlined />}>
              返回首页
            </Button>
          </Link>
        </div>
        
        <div className="mt-8 text-sm text-gray-400">
          <p>如果您认为这是一个错误，请联系管理员。</p>
        </div>
      </div>
    </div>
  )
}

import React from 'react'
import { useRouter, useParams } from 'next/navigation';
import ModelSelect from '@/app/components/ModelSelect';
import { Button, Popconfirm, PopconfirmProps, Dropdown, MenuProps } from "antd";

import { DeleteOutlined, G<PERSON><PERSON>Outlined, MenuOutlined, QuestionCircleOutlined, BugOutlined} from '@ant-design/icons';
import useSidebarCollapsedStore from '@/app/store/sidebarCollapsed';
import ToggleSidebar from "@/app/images/hideSidebar.svg";
import useChatStore from '@/app/store/chat';
import useChatListStore from '@/app/store/chatList';
import { deleteChatInServer } from '@/app/[workspace]/chat/actions/chat';
import { useTranslations } from 'next-intl';

const ChatHeader = (props: { isActionsHidden?: boolean }) => {
  const t = useTranslations('Chat');
  const router = useRouter();
  const params = useParams();
  const workspaceId = params.workspace as string;
  const { chatList, setChatList } = useChatListStore();
  const { chat } = useChatStore();
  const { isSidebarCollapsed, toggleSidebar } = useSidebarCollapsedStore();

  const deleteChat = async () => {
    if (chat && chat.id && workspaceId) {
      await deleteChatInServer(workspaceId, chat.id)
      const updatedChatList = chatList.filter(i => i.id !== chat.id);
      setChatList(updatedChatList);

      // 跳转到更新后的第一个聊天（如果存在）
      if (updatedChatList.length > 0) {
        router.push(`/${workspaceId}/chat/${updatedChatList[0].id}`);
      } else {
        router.push(`/${workspaceId}/chat`); // 如果没有聊天记录，跳回聊天首页
      }
    }
  };


  const confirmDelete: PopconfirmProps['onConfirm'] = (e) => {
    deleteChat()
  };

  const cancelDelete: PopconfirmProps['onCancel'] = (e) => { };

  // 下拉菜单配置
  const menuItems: MenuProps['items'] = [
    {
      key: 'github',
      label: (
        <a href="https://github.com/HiveNexus/HiveChat" target="_blank" rel="noopener noreferrer">
          GitHub
        </a>
      ),
      icon: <GithubOutlined />,
    },
    {
      key: 'help',
      label: (
        <a href="https://www.hivechat.net/docs" target="_blank" rel="noopener noreferrer">
          帮助中心
        </a>
      ),
      icon: <QuestionCircleOutlined />,
    },
    {
      key: 'feedback',
      label: (
        <a href="https://jsj.top/f/EpiJJR" target="_blank" rel="noopener noreferrer">
          反馈交流
        </a>
      ),
      icon: <BugOutlined />,
    },
  ];

  return (
    <div className="h-10 flex w-full bg-gray-50 shadow-sm grow-0  items-center p-2 justify-between">
      <div className='flex items-center'>
        {isSidebarCollapsed &&
          <Button
            icon={<ToggleSidebar style={{ 'fontSize': '20px', 'verticalAlign': 'middle' }} />}
            type='text'
            onClick={toggleSidebar}
          />
        }
        <ModelSelect chatId={chat?.id || null} />
      </div>
      <div className='mr-2'>
        {/* <Button type='text'
          icon={chat?.is_star ? <StarFilled style={{ color: '#f7b83c' }} /> : <StarOutlined style={{ color: 'gray' }}
          />} onClick={toggleStar} /> */}
        {!props.isActionsHidden &&
          <Popconfirm
            title={t('deleteCurrentChat')}
            description={t('clearHistoryMessageNotice')}
            onConfirm={confirmDelete}
            onCancel={cancelDelete}
            okText={t('confirm')}
            cancelText={t('cancel')}
          >
            <Button type='text'
              icon={<DeleteOutlined style={{ color: 'gray' }} />} />
          </Popconfirm>
        }
        <Dropdown
          menu={{ items: menuItems }}
          placement="bottomRight"
          trigger={['click']}
        >
          <Button type='text' icon={<MenuOutlined style={{ color: 'gray' }} />} />
        </Dropdown>
      </div>

    </div>
  )
}

export default ChatHeader

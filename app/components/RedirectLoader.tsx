"use client";
import { Spin } from 'antd';
import { useEffect, useState } from 'react';

interface RedirectLoaderProps {
  message?: string;
  delay?: number;
}

export default function RedirectLoader({ 
  message = "正在跳转...", 
  delay = 0 
}: RedirectLoaderProps) {
  const [show, setShow] = useState(delay === 0);

  useEffect(() => {
    if (delay > 0) {
      const timer = setTimeout(() => setShow(true), delay);
      return () => clearTimeout(timer);
    }
  }, [delay]);

  if (!show) return null;

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="text-center">
        <div className="mb-4">
          <Spin size="large" />
        </div>
        <p className="text-gray-600 text-lg">{message}</p>
        <p className="text-gray-400 text-sm mt-2">请稍候...</p>
      </div>
    </div>
  );
}

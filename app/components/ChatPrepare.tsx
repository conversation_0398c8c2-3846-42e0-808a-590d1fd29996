'use client';
import React, { useEffect } from 'react'
import { useParams } from 'next/navigation';
import useMcpServerStore from '@/app/store/mcp';
import useGlobalConfigStore from '@/app/store/globalConfig';
import { fetchSettingsByKeys, getMcpServersAndAvailableTools } from '@/app/[workspace]/chat/actions/chat';
import useModelListStore from '@/app/store/modelList';
import { fetchAvailableLlmModels, fetchAllProviders } from '@/app/[workspace]/admin/llm/actions';

const AppPrepare = () => {
  const params = useParams();
  const workspaceId = params.workspace as string;
  const { setHasUseMcp, setHasMcpSelected, setMcpServers, setAllTools } = useMcpServerStore();
  const { setChatNamingModel, setSearchEnable } = useGlobalConfigStore();

  useEffect(() => {
    const initializeMcpInfo = async () => {
      if (!workspaceId) return;
      const { mcpServers, tools } = await getMcpServersAndAvailableTools(workspaceId);
      setHasMcpSelected(false);
      if (mcpServers.length > 0) {
        setHasUseMcp(true);
        setMcpServers(mcpServers.map(server => ({
          ...server,
          type: server.type || 'sse',
          description: server.description ?? undefined,
        })));
        setAllTools(tools.map(tool => {
          const server = mcpServers.find(s => s.id === tool.serverId);
          return {
            id: tool.name,
            name: tool.name,
            serverName: server?.name || '',
            description: tool.description || undefined,
            inputSchema: JSON.parse(tool.inputSchema),
          };
        }));
      } else {
        setHasUseMcp(false);
        setMcpServers([]);
        setAllTools([]);
      }
    }
    initializeMcpInfo();
  }, [workspaceId, setHasUseMcp, setMcpServers, setHasMcpSelected, setAllTools]);

  const { initModelList, setCurrentModel, setIsPending, initAllProviderList } = useModelListStore();
  useEffect(() => {
    const initializeModelList = async () => {
      if (!workspaceId) return;
      try {
        const remoteModelList: any = await fetchAvailableLlmModels(workspaceId);
        initModelList(remoteModelList);
        const allProviderSettings = await fetchAllProviders(workspaceId);
        const processedList = allProviderSettings.map(item => ({
          id: item.provider,
          providerName: item.providerName,
          apiStyle: item.apiStyle,
          providerLogo: item.logo || '',
          status: item.isActive || false,
        }));
        initAllProviderList(processedList)
        setIsPending(false);
      } catch (error) {
        console.error('Error initializing model list:', error);
      }
    };

    initializeModelList();
  }, [workspaceId, initModelList, setCurrentModel, setIsPending, initAllProviderList]);

  useEffect(() => {
    const initializeAppSettings = async () => {
      const results = await fetchSettingsByKeys(['chatNamingModel', 'searchEnable'], workspaceId);
      if (results.chatNamingModel) {
        setChatNamingModel(results.chatNamingModel)
      } else {
        setChatNamingModel('current')
      }
      if (results.searchEnable) {
        setSearchEnable(results.searchEnable === 'true')
      }
    }
    initializeAppSettings();
  }, [setChatNamingModel, setSearchEnable, workspaceId]);
  return (
    <></>
  )
}

export default AppPrepare
'use client';
import React from 'react';
import { Button, Tooltip } from 'antd';
import { CustomerServiceOutlined } from '@ant-design/icons';

const CustomerService: React.FC = () => {
  const handleOpenCustomerService = () => {
    // 计算窗口居中位置
    const screenWidth = window.screen.width;
    const screenHeight = window.screen.height;
    const windowWidth = 360;
    const windowHeight = 550;
    
    const left = Math.round((screenWidth - windowWidth) / 2);
    const top = Math.round((screenHeight - windowHeight) / 2);

    // 打开新窗口
    const customerServiceWindow = window.open(
      'https://tawk.to/chat/68944c477f808f192df0de7f/1j21kju74',
      'customerService',
      `width=${windowWidth},height=${windowHeight},left=${left},top=${top},resizable=yes,scrollbars=yes,toolbar=no,menubar=no,location=no,status=no`
    );

    // 如果窗口被阻止，提示用户
    if (!customerServiceWindow) {
      alert('请允许弹窗以打开客服窗口');
    }
  };

  return (
    <Tooltip title="在线客服" placement="left">
      <Button
        type="primary"
        shape="circle"
        size="large"
        icon={<CustomerServiceOutlined />}
        onClick={handleOpenCustomerService}
        style={{
          position: 'fixed',
          bottom: '24px',
          right: '24px',
          zIndex: 1000,
          width: '56px',
          height: '56px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          fontSize: '20px',
        }}
        className="hover:scale-110 transition-transform duration-200"
      />
    </Tooltip>
  );
};

export default CustomerService;
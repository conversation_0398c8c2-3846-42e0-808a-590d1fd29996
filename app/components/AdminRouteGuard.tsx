'use client';
import { useEffect, useState, useRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { isWorkspaceAdmin } from '@/app/utils/workspace';

interface AdminRouteGuardProps {
  children: React.ReactNode;
}

export default function AdminRouteGuard({ children }: AdminRouteGuardProps) {
  const { data: session, status } = useSession();
  const params = useParams();
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(true);
  const [hasPermission, setHasPermission] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const workspaceId = params.workspace as string;

  useEffect(() => {
    async function checkPermission() {
      // 等待session加载完成
      if (status === 'loading') {
        return;
      }

      // 这种情况下重定向到登录页面
      if (!session?.user?.id) {
        console.warn('AdminRouteGuard: No session found, redirecting to login');
        router.replace('/login');
        return;
      }

      // 如果没有workspaceId，重定向到404
      if (!workspaceId) {
        console.warn('AdminRouteGuard: No workspace ID found');
        router.replace('/404');
        return;
      }

      try {
        setIsChecking(true);
        setError(null);
        const adminCheckResult = await isWorkspaceAdmin(workspaceId, session.user.id);

        if (adminCheckResult) {
          setHasPermission(true);
        } else {
          // 权限验证失败，重定向到404页面（隐藏路由存在性）
          console.warn('AdminRouteGuard: Permission denied for workspace:', workspaceId);
          router.replace('/404');
          return;
        }
      } catch (error) {
        console.error('AdminRouteGuard: Permission check error:', error);
        setError('权限验证失败');

        // 发生错误时也重定向到404页面
        router.replace('/404');
        return;
      } finally {
        setIsChecking(false);
      }
    }

    checkPermission();
  }, [session, status, workspaceId, router]);

  // 显示错误状态（通常不会到达这里，因为错误时会重定向）
  if (error) {
    return (
      <main className="h-dvh flex justify-center items-center">
        <div className="text-center">
          <div className="text-red-500 mb-2">权限验证失败</div>
          <div className="text-gray-500 text-sm">{error}</div>
        </div>
      </main>
    );
  }

  // 没有权限时不渲染内容（通常不会到达这里，因为会重定向）
  if (!hasPermission) {
    return null;
  }

  // 权限验证通过，渲染子组件
  return <>{children}</>;
}

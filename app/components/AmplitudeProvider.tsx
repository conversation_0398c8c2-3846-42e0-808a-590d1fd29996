'use client';

import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { initAmplitude, setUser } from '@/lib/amplitude';

export default function AmplitudeProvider() {
  const { data: session, status } = useSession();

  useEffect(() => {
    // 初始化 Amplitude
    initAmplitude();
  }, []);

  useEffect(() => {
    // 当用户登录状态确定且有用户信息时，设置用户 ID
    if (status === 'authenticated' && session?.user?.email) {
      setUser(session.user.email);
    }
  }, [session?.user?.email, status]);

  return null;
}

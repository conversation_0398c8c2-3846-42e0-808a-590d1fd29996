import React from 'react';
import { Modal, Form, Input, message } from 'antd';
import useModelListStore from '@/app/store/modelList';
import { saveToServer } from '@/app/[workspace]/admin/llm/actions';
import { useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';

type RenameProviderModalProps = {
  isModalOpen: boolean;
  setIsModalOpen: (open: boolean) => void;
  providerId: string;
  providerName: string;
};

const RenameProviderModal: React.FC<RenameProviderModalProps> = ({
  isModalOpen,
  setIsModalOpen,
  providerId,
  providerName,
}) => {
  const t = useTranslations('Admin.Models');
  const params = useParams();
  const workspaceId = params.workspace as string;
  const [renameProviderForm] = Form.useForm();
  const { renameProvider } = useModelListStore();

  const onModelFormSubmit = async (values: {
    providerId: string;
    providerName: string;
  }) => {
    renameProvider(values.providerId, values.providerName);

    const formData = new FormData();
    formData.append('providerId', values.providerId);
    formData.append('providerName', values.providerName);

    const result = await saveToServer(formData, workspaceId);
    if (result.status === 'success') {
      message.success('修改成功');
      setIsModalOpen(false);
    } else {
      message.error(result.message || '修改失败');
    }
  };

  return (
    <Modal
      title='服务商重命名'
      maskClosable={false}
      keyboard={false}
      centered={true}
      okText={t('okText')}
      cancelText={t('cancelText')}
      open={isModalOpen}
      onOk={() => renameProviderForm.submit()}
      onCancel={() => setIsModalOpen(false)}
    >
      <div className='mt-4'>
        <Form
          layout="vertical"
          form={renameProviderForm}
          onFinish={onModelFormSubmit}
          initialValues={{
            providerId: providerId,
            providerName: providerName,
          }}
        >
          <Form.Item
            name='providerId'
            label={<span className='font-medium'>服务商 ID</span>}
          >
            <Input disabled />
          </Form.Item>
          <Form.Item
            name='providerName'
            label={<span className='font-medium'>服务商名称</span>}
            rules={[{ required: true, message: '此项为必填' }]}
            extra="服务商显示名称，方便用户识别"
          >
            <Input type='text' />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default RenameProviderModal;
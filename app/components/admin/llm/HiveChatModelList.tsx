import { useEffect, useRef, useState, use<PERSON><PERSON>back, FC } from 'react';
import { Divide<PERSON>, Too<PERSON><PERSON>, Button, Popconfirm, message, Switch, Tag, Flex, Avatar } from 'antd';
import { EyeInvisibleOutlined, DeleteOutlined, SettingOutlined, PictureOutlined, ToolOutlined, HolderOutlined, GlobalOutlined } from '@ant-design/icons';
import useModelListStore from '@/app/store/modelList';
import { LLMModel } from '@/types/llm';
import { changeSelectInServer, deleteCustomModelInServer, saveModelsOrder, updateModelSelectedInServer } from '@/app/[workspace]/admin/llm/actions';
import ManageAllModelModal from '@/app/components/admin/llm/ManageAllModelModal';
import Sortable, { SortableEvent } from 'sortablejs';
import { useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';
import SearchIcon from "@/app/images/searchIcon.svg";
import { SwitchChangeEventHandler } from 'antd/es/switch';
import to from '@/app/utils/await-to';
import clsx from 'clsx';

interface ModelListProps {
  providerId: string;
  providerName: string;
}

const ListItem: FC<{item: LLMModel, providerId: string, draggable?: boolean, showItem?: boolean}> = ({item, providerId, draggable = true, showItem}) => {
  const t = useTranslations('Admin.Models');
  const params = useParams();
  const workspaceId = params.workspace as string;
  const { modelList, setModelList } = useModelListStore();
  const handleModelSwitch: SwitchChangeEventHandler = async (active) => {
    const newData = [...modelList]
    const current = newData.find(model => model.id === item.id)
    current!.selected = active
    setModelList(newData)

    const [err] = await to(updateModelSelectedInServer({
      selected: active,
      modelId: item.id,
      providerId,
      workspaceId
    }))

    if (err) {
      setModelList(modelList)
      message.error('切换失败')
    }
  }

  return <div key={item.id} className={clsx('flex flex-row', {
    'hidden': !item.selected && !showItem
  })}>
    <HolderOutlined className={
        clsx('cursor-move handle', {
          'pointer-events-none invisible': !draggable
        })
      }
      style={{ color: '#999', cursor: 'move' }}
    />
    <div className='flex flex-row ml-1 group w-full my-2 items-center h-12 bg-[#f8f8f8] rounded-md px-3 justify-between'>
      <div className='flex-1 flex flex-row items-center justify-between'>
        <Flex align="center" gap={10}>
          <Avatar src={item.logo} size={28}></Avatar>
          <span className='mr-2 text-[14px]'>{item.displayName}</span>
          <Tag color="#f1f1f1">
            <span className='text-[#666]'>{item.id}</span>
          </Tag>
        </Flex>
        <Flex align="center" gap={8}>
          <Flex align="center" gap={4}>
            {
              item?.supportVision && <Tooltip title={t('supportVision')}>
                <div style={{border: '1px solid rgba(117, 230, 5, 0.06)'}} className="bg-[#eaf4e6] text-[#379d4a] w-[20px] h-[20px] leading-[20px] text-center rounded-sm">
                  <PictureOutlined style={{fontSize: 12}}/>
                </div>
              </Tooltip>
            }
            {
              item?.supportTool && <><Tooltip title={t('supportTool')}>
                <div style={{border: '1px solid rgba(105, 55, 255, 0.02)'}} className="bg-[#f2f6ff] text-[#0072f5] w-[20px] h-[20px] leading-[20px] text-center rounded-sm">
                  <ToolOutlined style={{fontSize: 12}}/>
                </div>

              </Tooltip></>
            }
            {
              item?.builtInWebSearch && <>
              <Tooltip title='模型内置搜索'>
                <div style={{border: '1px solid rgba(255, 30, 155, 0.04)'}} className="bg-[#fff1fe] text-[#bd54c6] w-[20px] h-[20px] leading-[20px] text-center rounded-sm">
                  <GlobalOutlined style={{fontSize: 12}}/>
                </div>
              </Tooltip></>
            }
          </Flex>
          <Switch size='small' value={item.selected} onChange={handleModelSwitch}/>
        </Flex>
      </div>
    </div>
  </div>
}

const HiveChatModelList: React.FC<ModelListProps> = ({
  providerId,
  providerName,
}) => {
  const t = useTranslations('Admin.Models');
  const params = useParams();
  const workspaceId = params.workspace as string;
  const [isSorting, setIsSorting] = useState(false);
  const { modelList, setModelList } = useModelListStore();
  const listRef = useRef<HTMLDivElement>(null);
  const sortableRef = useRef<Sortable | null>(null);
  const inActiveList =  modelList.filter(model => !model.selected)

  const handleSortEnd = useCallback(async (evt: SortableEvent) => {
    if (evt.oldIndex === undefined || evt.newIndex === undefined) {
      return;
    }

    setIsSorting(true);
    try {
      const newModels = [...modelList];
      const [movedItem] = newModels.splice(evt.oldIndex, 1);
      newModels.splice(evt.newIndex, 0, movedItem);

      const newOrder = newModels.map((model, index) => ({ modelId: model.id, order: index }));
      setModelList(newModels);
      await saveModelsOrder(providerId, newOrder, workspaceId);
      message.success(t('saveSuccess'));
    } catch (error) {
      console.error('Failed to update order:', error);
      message.error(t('saveFailed'));
      // Revert to original order if save fails
      setModelList([...modelList]);
    } finally {
      setIsSorting(false);
    }
  }, [modelList, setModelList, providerId, workspaceId, t]);

  useEffect(() => {
    if (listRef.current && !sortableRef.current) {
      sortableRef.current = Sortable.create(listRef.current, {
        animation: 200,
        handle: '.handle',
        onEnd: handleSortEnd,
        disabled: isSorting,
      });
    }

    return () => {
      if (sortableRef.current) {
        sortableRef.current.destroy();
        sortableRef.current = null;
      }
    };
  }, [handleSortEnd, isSorting]);

  return (
    <>
      <div className='text-[#999] text-xs'>
      已启用
      </div>
      <div
        ref={listRef}
        className='text-xs text-gray-700 my-2 -ml-4 scrollbar-thin scrollbar-thumb-gray-300'>
        {modelList.map((item) => <ListItem providerId={providerId} item={item} key={item.id}/>)}
      </div>
      <div className='mt-[8px]'>
        <div className='text-[#999] text-xs'>未启用</div>
        <div className='text-xs text-gray-700 my-2 -ml-4'>
          {inActiveList.map((item) => <ListItem showItem draggable={false} providerId={providerId} item={item} key={item.id}/>)}
        </div>
      </div>
    </>
  );
};

export default HiveChatModelList;
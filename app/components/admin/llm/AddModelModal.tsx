import React from 'react';
import { Modal, Form, Input, Switch, message } from 'antd';
import useModelListStore from '@/app/store/modelList';
import { addCustomModelInServer } from '@/app/[workspace]/admin/llm/actions';
import { LLMModelProvider } from '@/types/llm';
import { useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';

type CustomModelModalProps = {
  isCustomModelModalOpen: boolean;
  setIsCustomModelModalOpen: (open: boolean) => void;
  provider: LLMModelProvider;
};

const AddModelModal: React.FC<CustomModelModalProps> = ({
  isCustomModelModalOpen,
  setIsCustomModelModalOpen,
  provider
}) => {
  const t = useTranslations('Admin.Models');
  const params = useParams();
  const workspaceId = params.workspace as string;
  const [customModelForm] = Form.useForm();
  const { addCustomModel } = useModelListStore();

  const onModelFormSubmit = async (values: {
    modelId: string;
    modelDisplayName: string;
    modelVisionSupport: boolean;
    modelToolSupport: boolean;
  }) => {

    const result = await addCustomModelInServer({
      name: values.modelId,
      displayName: values.modelDisplayName,
      maxTokens: null,
      supportVision: values.modelVisionSupport,
      supportTool: values.modelToolSupport,
      selected: true,
      type: 'custom',
      providerId: provider.id,
      providerName: provider.providerName
    }, workspaceId);
    if (result.status === 'success') {
      await addCustomModel({
        id: values.modelId,
        displayName: values.modelDisplayName,
        maxTokens: undefined,
        supportVision: values.modelVisionSupport,
        supportTool: values.modelToolSupport,
        selected: true,
        type: 'custom',
        provider: {
          id: provider.id,
          apiStyle: provider.apiStyle,
          providerName: provider.providerName
        }
      });
      message.success(t('addModelSuccess'));
      customModelForm.resetFields();
      setIsCustomModelModalOpen(false);
    } else {
      message.error('操作失败');
    }
  };

  return (
    <Modal
      title={t('addCustomModel')}
      maskClosable={false}
      keyboard={false}
      centered={true}
      okText={t('okText')}
      cancelText={t('cancelText')}
      open={isCustomModelModalOpen}
      onOk={() => customModelForm.submit()}
      onCancel={() => setIsCustomModelModalOpen(false)}
    >
      <div className='mt-4'>
        <Form
          layout="vertical"
          form={customModelForm}
          onFinish={onModelFormSubmit}
        >
          <Form.Item
            name='modelId'
            label={<span className='font-medium'>{t('modelId')}</span>}
            rules={[{ required: true, message: t('modelIdNotice') }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name='modelDisplayName'
            label={<span className='font-medium'>{t('modelDisplayName')}</span>}
            rules={[{ required: true, message: t('modelDisplayNameNotice') }]}
          >
            <Input type='text' />
          </Form.Item>

          <Form.Item
            name='modelVisionSupport'
            valuePropName="checked"
            label={<span className='font-medium'>{t('supportVision')}</span>}
          >
            <Switch defaultChecked={false} />
          </Form.Item>
          <Form.Item
            name='modelToolSupport'
            valuePropName="checked"
            label={<span className='font-medium'>支持工具调用</span>}
          >
            <Switch defaultChecked={false} />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default AddModelModal;
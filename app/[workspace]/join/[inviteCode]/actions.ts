'use server';
import { db } from '@/app/db';
import { workspaceInvites, userWorkspace, groups } from '@/app/db/schema';
import { eq, and } from 'drizzle-orm';
import { auth } from '@/auth';
import { createErrorResponse, createSuccessResponse } from '@/app/utils/workspace';

/**
 * 检查用户是否已经是工作空间的成员
 */
export async function checkUserWorkspaceMembership(workspaceId: string) {
  try {
    // 获取当前用户session
    const session = await auth();
    if (!session?.user?.id) {
      return {
        status: 'error',
        message: 'User not authenticated',
        isMember: false
      };
    }

    const userId = session.user.id;

    // 检查用户是否已经在工作空间中
    const existingUserWorkspace = await db.query.userWorkspace.findFirst({
      where: and(
        eq(userWorkspace.userId, userId),
        eq(userWorkspace.workspaceId, workspaceId),
        eq(userWorkspace.isActive, true)
      )
    });

    return {
      status: 'success',
      isMember: !!existingUserWorkspace,
      role: existingUserWorkspace?.role || null
    };

  } catch (error) {
    console.error('Check user workspace membership error:', error);
    return {
      status: 'error',
      message: 'Failed to check membership',
      isMember: false
    };
  }
}

/**
 * 通过邀请码加入工作空间
 */
export async function joinWorkspaceByInvite(workspaceId: string, inviteCode: string) {
  try {
    // 获取当前用户session
    const session = await auth();
    if (!session?.user?.id) {
      return createErrorResponse('User not authenticated');
    }

    const userId = session.user.id;

    // 验证邀请码是否有效
    const invite = await db.query.workspaceInvites.findFirst({
      where: and(
        eq(workspaceInvites.workspaceId, workspaceId),
        eq(workspaceInvites.inviteCode, inviteCode),
        eq(workspaceInvites.isActive, true)
      )
    });

    if (!invite) {
      return createErrorResponse('Invalid invite code');
    }

    // 检查邀请码是否过期
    const now = new Date();
    if (invite.expiresAt < now) {
      // 如果过期，将其设为非活跃状态
      await db.update(workspaceInvites)
        .set({ 
          isActive: false,
          updatedAt: new Date()
        })
        .where(eq(workspaceInvites.id, invite.id));
      
      return createErrorResponse('Invite code expired');
    }

    // 检查用户是否已经在工作空间中
    const existingUserWorkspace = await db.query.userWorkspace.findFirst({
      where: and(
        eq(userWorkspace.userId, userId),
        eq(userWorkspace.workspaceId, workspaceId)
      )
    });

    if (existingUserWorkspace) {
      if (existingUserWorkspace.isActive) {
        return createErrorResponse('You are already a member of this workspace');
      } else {
        // 如果用户之前在工作空间中但被禁用，重新激活
        await db.update(userWorkspace)
          .set({
            isActive: true,
            joinedAt: new Date(),
            updatedAt: new Date()
          })
          .where(and(
            eq(userWorkspace.userId, userId),
            eq(userWorkspace.workspaceId, workspaceId)
          ));

        return createSuccessResponse({ message: 'Successfully rejoined workspace' });
      }
    }

    // 获取默认分组
    const defaultGroup = await db.query.groups.findFirst({
      where: and(
        eq(groups.workspaceId, workspaceId),
        eq(groups.isDefault, true)
      )
    });

    if (!defaultGroup) {
      return createErrorResponse('Default group not found in workspace');
    }

    // 将用户添加到工作空间
    await db.insert(userWorkspace).values({
      userId,
      workspaceId,
      role: 'member',
      isActive: true,
      joinedAt: new Date(),
      groupId: defaultGroup.id,
      todayTotalTokens: 0,
      currentMonthTotalTokens: 0,
      usageUpdatedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date()
    });

    return createSuccessResponse({ 
      message: 'Successfully joined workspace',
      workspaceId,
      groupId: defaultGroup.id
    });

  } catch (error) {
    console.error('Join workspace by invite error:', error);
    return createErrorResponse('Failed to join workspace');
  }
}

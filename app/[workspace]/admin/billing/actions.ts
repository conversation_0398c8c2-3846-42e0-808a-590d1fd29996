'use server';

import { db } from '@/app/db';
import { purchaseOrders, users } from '@/app/db/schema';
import { eq, and, desc } from 'drizzle-orm';
import { isWorkspaceAdmin } from '@/app/utils/workspace';
import { auth } from '@/auth';

// 订单数据类型定义
export interface OrderRecord {
  id: string;
  packageType: 'product_3' | 'product_10' | 'product_100';
  creditsPurchased: number;
  amountPaidCents: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed';
  userEmail: string;
  userName?: string;
  completedAt?: Date;
  createdAt: Date;
  stripeSessionId: string;
}

// 产品配置映射
const PRODUCT_CONFIG = {
  product_3: { name: '基础套餐', credits: 30000, amount: 300 },
  product_10: { name: '标准套餐', credits: 100000, amount: 1000 },
  product_100: { name: '专业套餐', credits: 1000000, amount: 10000 }
};

// 通用错误处理
const handleDatabaseError = (error: unknown, defaultMessage: string) => ({
  success: false,
  message: error instanceof Error ? error.message : defaultMessage
});

// 通用成功响应
const createSuccessResponse = (data?: any) => ({
  success: true,
  data,
  message: '操作成功'
});

// 通用错误响应
const createErrorResponse = (message: string) => ({
  success: false,
  message
});

/**
 * 获取订单列表
 */
export async function getOrderList(workspaceId: string): Promise<{
  success: boolean;
  data?: OrderRecord[];
  message?: string;
}> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return createErrorResponse('未授权访问');
    }

    // 验证用户是否为工作空间管理员
    const adminCheck = await isWorkspaceAdmin(workspaceId, session.user.id);
    if (!adminCheck) {
      return createErrorResponse('权限不足，仅管理员可访问');
    }

    // 查询订单列表，关联用户信息
    const orders = await db
      .select({
        id: purchaseOrders.id,
        packageType: purchaseOrders.packageType,
        creditsPurchased: purchaseOrders.creditsPurchased,
        amountPaidCents: purchaseOrders.amountPaidCents,
        currency: purchaseOrders.currency,
        status: purchaseOrders.status,
        userEmail: users.email,
        userName: users.name,
        completedAt: purchaseOrders.completedAt,
        createdAt: purchaseOrders.createdAt,
        stripeSessionId: purchaseOrders.stripeSessionId,
      })
      .from(purchaseOrders)
      .leftJoin(users, eq(purchaseOrders.userId, users.id))
      .where(eq(purchaseOrders.workspaceId, workspaceId))
      .orderBy(desc(purchaseOrders.createdAt));

    return createSuccessResponse(orders);
  } catch (error) {
    console.error('获取订单列表失败:', error);
    return handleDatabaseError(error, '获取订单列表失败');
  }
}

/**
 * 删除订单（仅限已取消状态的订单）
 */
export async function deleteOrder(workspaceId: string, orderId: string): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return createErrorResponse('未授权访问');
    }

    // 验证用户是否为工作空间管理员
    const adminCheck = await isWorkspaceAdmin(workspaceId, session.user.id);
    if (!adminCheck) {
      return createErrorResponse('权限不足，仅管理员可访问');
    }

    // 查找订单并验证状态
    const order = await db.query.purchaseOrders.findFirst({
      where: and(
        eq(purchaseOrders.id, orderId),
        eq(purchaseOrders.workspaceId, workspaceId)
      )
    });

    if (!order) {
      return createErrorResponse('订单不存在');
    }

    // 只允许删除失败状态的订单
    if (order.status !== 'failed') {
      return createErrorResponse('只能删除已取消状态的订单');
    }

    // 删除订单
    await db.delete(purchaseOrders)
      .where(and(
        eq(purchaseOrders.id, orderId),
        eq(purchaseOrders.workspaceId, workspaceId)
      ));

    return createSuccessResponse();
  } catch (error) {
    console.error('删除订单失败:', error);
    return handleDatabaseError(error, '删除订单失败');
  }
}

/**
 * 获取产品配置信息
 */
export async function getProductConfig() {
  return PRODUCT_CONFIG;
}

/**
 * 重新发起支付（针对待支付订单）
 */
export async function retryPayment(workspaceId: string, orderId: string): Promise<{
  success: boolean;
  data?: { url: string; packageType?: string; orderId?: string };
  message: string;
}> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return createErrorResponse('未授权访问');
    }

    // 验证用户是否为工作空间管理员
    const adminCheck = await isWorkspaceAdmin(workspaceId, session.user.id);
    if (!adminCheck) {
      return createErrorResponse('权限不足，仅管理员可访问');
    }

    // 查找订单
    const order = await db.query.purchaseOrders.findFirst({
      where: and(
        eq(purchaseOrders.id, orderId),
        eq(purchaseOrders.workspaceId, workspaceId)
      )
    });

    if (!order) {
      return createErrorResponse('订单不存在');
    }

    // 只允许对待支付状态的订单重新发起支付
    if (order.status !== 'pending') {
      return createErrorResponse('只能对待支付状态的订单重新发起支付');
    }

    // 构建支付链接，使用现有的 checkout_sessions API
    // 传递 packageType 参数来创建新的支付会话
    const paymentUrl = `/api/${workspaceId}/checkout_sessions`;

    return {
      success: true,
      data: {
        url: paymentUrl,
        packageType: order.packageType,
        orderId: order.id
      },
      message: '请重新创建支付会话'
    };
  } catch (error) {
    console.error('重新发起支付失败:', error);
    return handleDatabaseError(error, '重新发起支付失败');
  }
}

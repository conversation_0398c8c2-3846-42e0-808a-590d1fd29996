'use client';
import React, { useEffect, useState, useCallback } from 'react';
import { Button, message, Skeleton, Tag, Tooltip, Space, Popconfirm } from 'antd';
import { useParams } from 'next/navigation';
import { useWorkspaceAdmin } from '@/app/hooks/useWorkspaceAdmin';
import { getOrderList, deleteOrder, OrderRecord } from './actions';
import { DeleteOutlined } from '@ant-design/icons';
import CustomerService from '@/app/components/CustomerService';

const BillingPage = () => {
  const params = useParams();
  const workspaceId = params.workspace as string;
  const [orderList, setOrderList] = useState<OrderRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // 检查当前用户是否为管理员（用于权限控制）
  useWorkspaceAdmin(workspaceId);

  // 产品配置
  const productConfig = {
    product_3: { name: '基础套餐', credits: 30000, amount: 300 },
    product_10: { name: '标准套餐', credits: 100000, amount: 1000 },
    product_100: { name: '专业套餐', credits: 1000000, amount: 10000 }
  };

  // 获取订单列表
  const fetchOrderList = useCallback(async () => {
    try {
      const result = await getOrderList(workspaceId);
      if (result.success && result.data) {
        setOrderList(result.data);
      } else {
        message.error(result.message || '获取订单列表失败');
      }
    } catch (error) {
      console.error('获取订单列表失败:', error);
      message.error('获取订单列表失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [workspaceId]);

  // 删除订单
  const handleDeleteOrder = async (orderId: string) => {
    try {
      const result = await deleteOrder(workspaceId, orderId);
      if (result.success) {
        message.success('订单删除成功');
        await fetchOrderList();
      } else {
        message.error(result.message || '删除订单失败');
      }
    } catch (error) {
      console.error('删除订单失败:', error);
      message.error('删除订单失败');
    }
  };

  // 继续支付现有订单
  const handleRetryPayment = async (packageType: string, orderId?: string) => {
    try {
      // 调用专门的继续支付 API
      const response = await fetch(`/api/${workspaceId}/continue-payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: packageType,
          orderId: orderId // 如果有订单ID，优先使用订单ID
        }),
      });

      const data = await response.json();

      if (response.ok && data.url) {
        // 跳转到支付页面
        window.open(data.url, '_blank');
        if (data.isExistingSession) {
          message.success('已恢复您的支付会话，请在新窗口中完成支付');
        } else {
          message.success('已为您的订单创建新的支付会话，请在新窗口中完成支付');
        }
      } else {
        // 如果继续支付失败，提示用户可能需要创建新订单
        if (response.status === 404 || response.status === 410) {
          message.warning(data.error + ' 请创建新订单。');
        } else {
          message.error(data.error || '继续支付失败');
        }
      }
    } catch (error) {
      console.error('继续支付失败:', error);
      message.error('继续支付失败');
    }
  };

  // 格式化金额显示
  const formatAmount = (amountCents: number, currency: string) => {
    const amount = amountCents / 100;
    return `$${amount.toFixed(2)} ${currency.toUpperCase()}`;
  };

  // 格式化时间显示
  const formatDateTime = (date: Date | string | null | undefined) => {
    if (!date) return '-';
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    switch (status) {
      case 'pending':
        return <Tag color="orange">待支付</Tag>;
      case 'completed':
        return <Tag color="green">已完成</Tag>;
      case 'failed':
        return <Tag color="red">已取消</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  // 获取产品类型显示名称
  const getProductTypeName = (packageType: string) => {
    return productConfig[packageType as keyof typeof productConfig]?.name || packageType;
  };

  useEffect(() => {
    fetchOrderList();
  }, [fetchOrderList]);

  return (
    <div className='container mb-6 px-4 md:px-4 pt-6'>
      <div className='w-full mb-6 flex flex-row justify-between items-center'>
        <h1 className='text-2xl font-bold'>订单管理</h1>
      </div>

      {loading ? (
        <Skeleton active />
      ) : (
        <div className="overflow-hidden rounded-lg border border-slate-300">
          <table className='border-collapse w-full'>
            <thead>
              <tr className="bg-slate-100 text-sm">
                <th className='border-b border-r border-slate-300 p-2 w-72'>订单 ID</th>
                <th className='border-b border-r border-slate-300 p-2'>支付金额</th>
                <th className='border-b border-r border-slate-300 p-2'>获得积分</th>
                <th className='border-b border-r border-slate-300 p-2'>操作用户</th>
                <th className='border-b border-r border-slate-300 p-2 text-center'>状态</th>
                <th className='border-b border-r border-slate-300 p-2 text-center'>创建时间</th>
                <th className='border-b border-slate-300 p-2 text-center w-24'>操作</th>
              </tr>
            </thead>
            <tbody>
              {orderList.length === 0 ? (
                <tr>
                  <td colSpan={8} className='border-t  text-sm text-center text-gray-500 p-8'>
                    暂无订单数据
                  </td>
                </tr>
              ) : (
                orderList.map((order) => (
                  <tr key={order.id} className="hover:bg-slate-50">
                    <td className='border-t border-r text-sm border-slate-300 p-2'>
                      <span className='font-mono text-xs'>
                        {order.id}
                      </span>
                    </td>

                    <td className='border-t border-r text-sm text-right border-slate-300 p-2'>
                      {formatAmount(order.amountPaidCents, order.currency)}
                    </td>
                    <td className='border-t border-r text-sm text-right border-slate-300 p-2'>
                      {order.creditsPurchased.toLocaleString()} 积分
                    </td>
                    <td className='border-t border-r text-sm border-slate-300 p-2'>
                      <div>
                        <div className=' text-gray-500'>{order.userEmail}</div>
                      </div>
                    </td>
                    <td className='border-t border-r text-sm text-center border-slate-300 p-2'>
                      {getStatusTag(order.status)}
                    </td>
                    <td className='border-t border-r text-xs text-center border-slate-300 p-2'>
                      {formatDateTime(order.createdAt)}
                    </td>
                    <td className='border-t text-center text-sm border-slate-300 p-2'>
                      <Space size="small">
                        {order.status === 'failed' && (
                          <Popconfirm
                            title="确认删除"
                            description="确定要删除这个订单吗？此操作不可恢复。"
                            onConfirm={() => handleDeleteOrder(order.id)}
                            okText="确认"
                            cancelText="取消"
                          >
                            <Button
                              size='small'
                              type='link'
                              danger
                              icon={<DeleteOutlined />}
                            >
                              删除
                            </Button>
                          </Popconfirm>
                        )}
                        {order.status === 'pending' && (
                          <Button
                            size='small'
                            type='link'
                            onClick={() => handleRetryPayment(order.packageType, order.id)}
                          >
                            继续支付
                          </Button>
                        )}
                        {order.status === 'completed' && (
                          <span>-</span>
                        )}
                      </Space>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      )}

      <div className='mt-4 text-sm text-gray-500'>
        共 {orderList.length} 条订单记录
      </div>
      
      {/* 客服组件 */}
      <CustomerService />
    </div>
  );
};

export default BillingPage;
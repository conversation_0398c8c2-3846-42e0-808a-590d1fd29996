'use server';
import { mcpServers, mcpTools } from '@/app/db/schema';
import { MCPTool } from '@/types/llm';
import mcpService from '@/app/services/MCPService';
import { db } from '@/app/db';

import { eq, and } from 'drizzle-orm';
import { isWorkspaceAdmin, createErrorResponse, createSuccessResponse } from '@/app/utils/workspace';

export async function getMcpServerList(workspaceId: string) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return createErrorResponse('Access denied');
  }

  try {
    const result = await db.query.mcpServers.findMany({
      where: eq(mcpServers.workspaceId, workspaceId),
      orderBy: [mcpServers.createdAt],
    });

    return createSuccessResponse(result.map(server => ({
      ...server,
      type: server.type || 'sse'
    })));
  } catch (error) {
    console.error('Error fetching MCP server list:', error);
    return createErrorResponse('Failed to fetch MCP server list');
  }
}

export async function addMcpServer(formData: FormData) {
  // 验证workspace管理员权限
  const workspaceId = formData.get('workspaceId') as string;
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return createErrorResponse('Access denied');
  }

  const mcpServerInfo = {
    name: formData.get('name') as string,
    isActive: formData.get('isActive') === 'true',
    type: formData.get('type') as 'sse' | 'streamableHttp',
    description: formData.get('description') as string || undefined,
    baseUrl: formData.get('baseUrl') as string,
  };

  if (!mcpServerInfo.name || !mcpServerInfo.baseUrl) {
    return createErrorResponse('Name and baseUrl are required');
  }

  try {
    const existingServer = await db.query.mcpServers.findFirst({
      where: and(
        eq(mcpServers.name, mcpServerInfo.name),
        eq(mcpServers.workspaceId, workspaceId)
      ),
    });

    if (existingServer) {
      return createErrorResponse(`MCP Server ${mcpServerInfo.name} 已存在`);
    }

    // 连接测试
    if (mcpServerInfo.isActive) {
      try {
        const tools = await mcpService.listTools(mcpServerInfo);
        // 工具数量大于 0 才支持添加
        if (tools.length > 0) {
          const serverResult = await db.insert(mcpServers).values({
            ...mcpServerInfo,
            workspaceId
          }).returning();
          const insertedServer = serverResult[0];
          await db.insert(mcpTools).values(tools.map(tool => ({
            name: tool.name,
            serverId: insertedServer.id,
            description: tool.description,
            inputSchema: JSON.stringify(tool.inputSchema),
          })));
        } else {
          return createErrorResponse('MCP Server 未提供 Tool， 暂不支持添加');
        }
      } catch (error) {
        return createErrorResponse(`添加失败：${(error as Error).message}`);
      }
    } else {
      await db.insert(mcpServers).values({
        ...mcpServerInfo,
        workspaceId
      });
    }

    return createSuccessResponse();
  } catch (error) {
    console.error('Error adding MCP server:', error);
    return createErrorResponse('Database add error');
  }
}

export async function updateMcpServer(serverId: string, mcpServerInfo: {
  name: string;
  isActive: boolean;
  description?: string;
  type: 'sse' | 'streamableHttp';
  baseUrl?: string;
}, workspaceId: string) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    throw new Error('Access denied');
  }
  try {
    const existingServer = await db.query.mcpServers.findFirst({
      where: and(
        eq(mcpServers.id, serverId),
        eq(mcpServers.workspaceId, workspaceId)
      ),
    });

    if (!existingServer) {
      return {
        success: false,
        message: '此 MCP 服务器不存在'
      }
    } else {
      await db.update(mcpServers)
        .set(mcpServerInfo)
        .where(and(
          eq(mcpServers.id, serverId),
          eq(mcpServers.workspaceId, workspaceId)
        ));
      if (mcpServerInfo.isActive) {
        try {
          // 删除原有的工具，再新增
          await db.delete(mcpTools).where(eq(mcpTools.serverId, existingServer.id));
          // 新增新的工具
          await mcpService.removeServer(mcpServerInfo);
          const tools = await mcpService.listTools(mcpServerInfo);
          await db.insert(mcpTools).values(tools.map(tool => ({
            name: tool.name,
            serverId: existingServer.id,
            description: tool.description || '',
            inputSchema: JSON.stringify(tool.inputSchema),
          })));
        } catch (error) {
          return {
            success: false,
            message: 'connect MCP server error:' + (error as Error).message
          }
        }
      } else {
        mcpService.deactivate(existingServer.name);
      }
      return {
        success: true,
        message: '已更新',
      };
    }
  } catch (error) {
    return {
      success: false,
      message: 'database update error' + (error as Error).message
    }
  }
}

export async function deleteMcpServer(name: string, workspaceId: string) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return createErrorResponse('Access denied');
  }

  try {
    await db.delete(mcpServers).where(
      and(
        eq(mcpServers.name, name),
        eq(mcpServers.workspaceId, workspaceId)
      )
    );
    mcpService.deleteServer(name);

    return createSuccessResponse();
  } catch (error) {
    console.error('Error deleting MCP server:', error);
    return createErrorResponse('Database delete error');
  }
}

export async function fetchToolList(serverName: string, workspaceId: string): Promise<MCPTool[]> {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return [];
  }

  try {
    const server = await db.query.mcpServers.findFirst({
      where: and(
        eq(mcpServers.name, serverName),
        eq(mcpServers.workspaceId, workspaceId)
      ),
    });

    if (!server) return [];

    const result = await db.query.mcpTools.findMany({
      where: eq(mcpTools.serverId, server.id),
    });
    return result.map(item => ({
      id: item.name,
      name: item.name,
      description: item.description || undefined,
      serverName: serverName,
      inputSchema: JSON.parse(item.inputSchema)
    }));
  } catch (error) {
    console.error('Error fetching tool list:', error);
    return [];
  }
}

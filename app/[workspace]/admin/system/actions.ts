'use server';
import { db } from '@/app/db';
import { eq, and } from 'drizzle-orm'
import { appSettings } from '@/app/db/schema';
import { isWorkspaceAdmin } from '@/app/utils/workspace';

export const fetchAppSettings = async (key: string, workspaceId: string) => {
  const result = await db.query.appSettings
    .findFirst({
      where: and(
        eq(appSettings.key, key),
        eq(appSettings.workspaceId, workspaceId)
      )
    });
  return result?.value;
}

export const adminAndSetAppSettings = async (key: string, newValue: string, workspaceId: string): Promise<{
  status: string;
  message?: string;
}> => {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return {
      status: 'fail',
      message: 'Access denied'
    }
  }

  return setAppSettings(key, newValue, workspaceId);
}

export const setAppSettings = async (key: string, newValue: string, workspaceId: string): Promise<{
  status: string;
  message?: string;
}> => {

  const result = await db.query.appSettings
    .findFirst({
      where: and(
        eq(appSettings.key, key),
        eq(appSettings.workspaceId, workspaceId)
      )
    });
  if (result) {
    await db.update(appSettings)
      .set({
        value: newValue,
        updatedAt: new Date(),
      })
      .where(and(
        eq(appSettings.key, key),
        eq(appSettings.workspaceId, workspaceId)
      ));
  } else {
    await db.insert(appSettings).values({
      key: key,
      value: newValue,
      workspaceId: workspaceId,
      updatedAt: new Date()
    });
  }
  return {
    status: 'success'
  }
}
'use server';
import WebSearchService from '@/app/services/WebSearchService';
import { searchEngineConfig } from '@/app/db/schema';
import { db } from '@/app/db';
import { eq, and } from 'drizzle-orm';
import { isWorkspaceAdmin, createErrorResponse, createSuccessResponse } from '@/app/utils/workspace';

export async function getDefaultSearchEngineConfig(workspaceId: string) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return createErrorResponse('Access denied');
  }

  try {
    const result = await db.query.searchEngineConfig.findFirst({
      where: and(
        eq(searchEngineConfig.isActive, true),
        eq(searchEngineConfig.workspaceId, workspaceId)
      )
    });

    if (result) {
      return createSuccessResponse(result);
    } else {
      // Check if tavily config exists
      const tavilyConfig = await db.query.searchEngineConfig.findFirst({
        where: and(
          eq(searchEngineConfig.id, 'tavily'),
          eq(searchEngineConfig.workspaceId, workspaceId)
        )
      });

      if (tavilyConfig) {
        // If tavily config exists, set it as active
        await db.update(searchEngineConfig)
          .set({ isActive: true })
          .where(and(
            eq(searchEngineConfig.id, 'tavily'),
            eq(searchEngineConfig.workspaceId, workspaceId)
          ));
        return createSuccessResponse({ ...tavilyConfig, isActive: true });
      } else {
        // If no config exists at all, create new tavily config
        const newConfig = {
          id: 'tavily',
          name: 'Tavily',
          apiKey: '',
          isActive: true,
          extractKeywords: false,
          maxResults: 5,
          workspaceId: workspaceId,
        };
        await db.insert(searchEngineConfig).values(newConfig);
        return createSuccessResponse(newConfig);
      }
    }
  } catch (error) {
    console.error('Error getting search engine config:', error);
    return createErrorResponse('Failed to get search engine config');
  }
}

export async function setSearchEngineConfig(searchEngineId: string, workspaceId: string) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return createErrorResponse('Access denied');
  }

  try {
    // First, set all search engines to inactive for this workspace
    await db.update(searchEngineConfig)
      .set({ isActive: false })
      .where(and(
        eq(searchEngineConfig.isActive, true),
        eq(searchEngineConfig.workspaceId, workspaceId)
      ));

    // Then, get the target search engine config
    const targetConfig = await db.query.searchEngineConfig.findFirst({
      where: and(
        eq(searchEngineConfig.id, searchEngineId),
        eq(searchEngineConfig.workspaceId, workspaceId)
      )
    });

    if (targetConfig) {
      // If config exists, update it to active
      await db.update(searchEngineConfig)
        .set({ isActive: true })
        .where(and(
          eq(searchEngineConfig.id, searchEngineId),
          eq(searchEngineConfig.workspaceId, workspaceId)
        ));
      return createSuccessResponse(targetConfig);
    } else {
      // If config doesn't exist, create a new one
      const newConfig = {
        id: searchEngineId,
        name: searchEngineId,
        apiKey: '',
        isActive: true,
        extractKeywords: false,
        maxResults: 5,
        workspaceId: workspaceId,
      };
      await db.insert(searchEngineConfig).values(newConfig);
      return createSuccessResponse(newConfig);
    }
  } catch (error) {
    console.error('Error setting search engine config:', error);
    return createErrorResponse('Failed to set search engine config');
  }
}

export async function saveSearchEngineConfig(workspaceId: string) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return createErrorResponse('Access denied');
  }

  try {
    const result = await db.query.searchEngineConfig.findFirst({
      where: and(
        eq(searchEngineConfig.isActive, true),
        eq(searchEngineConfig.workspaceId, workspaceId)
      )
    });

    if (result) {
      return createSuccessResponse(result);
    } else {
      const defaultConfig = {
        id: 'tavily',
        name: 'Tavily',
        apiKey: '',
        maxResults: 5,
      };
      return createSuccessResponse(defaultConfig);
    }
  } catch (error) {
    console.error('Error saving search engine config:', error);
    return createErrorResponse('Failed to save search engine config');
  }
}

export async function updateSearchEngineConfig(config: {
  id: string;
  name: string;
  apiKey: string | null;
  isActive: boolean;
  extractKeywords: boolean;
  maxResults: number;
}, workspaceId: string) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return createErrorResponse('Access denied');
  }

  try {
    await db.update(searchEngineConfig)
      .set(config)
      .where(and(
        eq(searchEngineConfig.id, config.id),
        eq(searchEngineConfig.workspaceId, workspaceId)
      ));

    return createSuccessResponse();
  } catch (error) {
    console.error('Error updating search engine config:', error);
    return createErrorResponse('Failed to update search engine config');
  }
}

export async function disableAllSearchEngines(workspaceId: string) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return createErrorResponse('Access denied');
  }

  try {
    // 将所有搜索引擎设置为非激活状态
    await db.update(searchEngineConfig)
      .set({ isActive: false })
      .where(eq(searchEngineConfig.workspaceId, workspaceId));

    return createSuccessResponse();
  } catch (error) {
    console.error('Error disabling search engines:', error);
    return createErrorResponse('Failed to disable search engines');
  }
}

export const checkSearch = async (providerId: string, apiKey: string, workspaceId: string): Promise<{ valid: boolean; message?: string }> => {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return { valid: false, message: 'Access denied' };
  }

  try {
    const result = await WebSearchService.checkSearch({
      id: providerId,
      name: providerId,
      apiKey: apiKey,
    });

    return {
      valid: result.valid,
      message: result.error?.message,
    };
  } catch (error) {
    console.error('Error checking search:', error);
    return { valid: false, message: 'Failed to check search' };
  }
}
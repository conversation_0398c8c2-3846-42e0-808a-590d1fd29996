'use client';

import React, { useEffect, useState } from 'react';
import { useSearchParams, useParams, useRouter } from 'next/navigation';
import { Result, But<PERSON>, Spin, Divider } from 'antd';
import { CheckCircleOutlined, LoadingOutlined, ExclamationCircleOutlined } from '@ant-design/icons';

interface PaymentResult {
  success: boolean;
  status: string;
  message: string;
  order?: {
    id: string;
    creditsPurchased: number;
    amountPaid: number;
  };
}

const PaymentSuccessPage = () => {
  const searchParams = useSearchParams();
  const params = useParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [paymentResult, setPaymentResult] = useState<PaymentResult | null>(null);

  const sessionId = searchParams.get('session_id');
  const workspaceId = params.workspace as string;

  useEffect(() => {
    if (!sessionId) {
      setError('缺少支付会话ID');
      setLoading(false);
      return;
    }

    // 验证支付状态
    const verifyPayment = async () => {
      try {
        const response = await fetch(`/api/${workspaceId}/verify-payment`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            sessionId,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || '支付验证失败');
        }

        const result: PaymentResult = await response.json();
        setPaymentResult(result);

        if (!result.success) {
          setError(result.message || '支付验证失败');
        }
      } catch (error) {
        console.error('支付验证失败:', error);
        setError(error instanceof Error ? error.message : '支付验证失败，请稍后重试');
      } finally {
        setLoading(false);
      }
    };

    verifyPayment();
  }, [sessionId, workspaceId]);

  const handleBackToPlans = () => {
    router.push(`/${workspaceId}/admin/plans`);
  };

  const handleBackToBilling = () => {
    router.push(`/${workspaceId}/admin/billing`);
  };

  const handleGoToChat = () => {
    router.push(`/${workspaceId}/chat`);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Spin
            indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}
            size="large"
          />
          <div className="mt-4 text-lg text-gray-600">
            正在处理您的支付...
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Result
          status="error"
          title="支付验证失败"
          subTitle={error}
          extra={[
            <Button type="primary" key="back" onClick={handleBackToPlans}>
              返回计划页面
            </Button>
          ]}
        />
      </div>
    );
  }

  // 如果支付验证成功但状态不是成功，显示相应状态
  if (paymentResult && !paymentResult.success) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Result
          icon={<ExclamationCircleOutlined className="text-orange-500" />}
          status="warning"
          title="支付处理中"
          subTitle={paymentResult.message || '支付正在处理中，请稍后查看'}
          extra={[
            <Button type="primary" key="refresh" onClick={() => window.location.reload()}>
              刷新页面
            </Button>,
            <Button key="back" onClick={handleBackToPlans}>
              返回计划页面
            </Button>
          ]}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center w-full bg-gray-50">
      <div className="max-w-md w-full mx-4 bg-white rounded-md">
        <Result
          icon={<CheckCircleOutlined className="text-green-500" />}
          status="success"
          title="支付成功！"
          subTitle={
            paymentResult?.order
              ? `您已成功购买 ${paymentResult.order.creditsPurchased.toLocaleString()} 积分，金额 $${paymentResult.order.amountPaid}，积分已到账。`
              : "您的积分已成功充值到账户，现在可以开始使用了。"
          }
          extra={[
            <Button type="primary" key="chat" onClick={handleGoToChat}>
              开始对话
            </Button>,
            <Button key="plans" onClick={handleBackToBilling}>
              查看账单
            </Button>
          ]}
        />

        {paymentResult?.order && (
          <div className="mt-4 bg-blue-50 rounded-lg p-4 shadow-sm mx-4">
            <h4 className="text-sm font-medium text-blue-900 mb-2">购买详情</h4>
            <div className="text-sm text-blue-800 space-y-1">
              <p>• 订单号：{paymentResult.order.id}</p>
              <p>• 购买积分：{paymentResult.order.creditsPurchased.toLocaleString()}</p>
              <p>• 支付金额：${paymentResult.order.amountPaid}</p>
            </div>
          </div>
        )}

        <div className="mt-2 bg-white rounded-lg p-6 shadow-sm">
          <Divider >
            <span className="text-sm font-medium text-gray-700">接下来您可以</span>
          </Divider>
          <ul className="space-y-2 text-sm text-gray-600">
            <li>• 在聊天页面开始与 AI 对话</li>
            <li>• 查看使用情况和积分余额</li>
            <li>• 邀请团队成员共享积分</li>
            <li>• 配置自定义 API Key</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default PaymentSuccessPage;

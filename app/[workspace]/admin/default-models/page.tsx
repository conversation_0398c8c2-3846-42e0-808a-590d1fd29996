'use client';
import React, { useEffect } from 'react';
import useModelListStore from '@/app/store/modelList';
import { fetchAvailableLlmModels } from '@/app/[workspace]/admin/llm/actions';
import ChatNaming from '@/app/components/admin/ChatNaming';
import { Button } from 'antd';
import ToggleSidebar from "@/app/images/hideSidebar.svg";
import useAdminSidebarCollapsed from '@/app/store/adminSidebarCollapsed';
import DefaultChatModel from '@/app/components/admin/DefaultChatModel';
import { useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';

const Userpage = () => {
  const t = useTranslations('Admin.System');
  const params = useParams();
  const workspaceId = params.workspace as string;
  const { initModelList } = useModelListStore();
  const { isSidebarCollapsed, toggleSidebar } = useAdminSidebarCollapsed();
  useEffect(() => {
    const initializeModelList = async () => {
      if (!workspaceId) return;
      try {
        const remoteModelList = await fetchAvailableLlmModels(workspaceId, false);
        initModelList(remoteModelList);
      } catch (error) {
        console.error("Error fetching model list:", error);
      }
    };
    initializeModelList();
  }, [workspaceId, initModelList]);
  return (
    <div className='flex flex-col w-full items-center'>
      <div className='flex flex-row w-full items-center h-10 px-1'>
        {isSidebarCollapsed &&
          <Button
            icon={<ToggleSidebar style={{ 'color': '#999', 'fontSize': '20px', 'verticalAlign': 'middle' }} />}
            type='text'
            onClick={toggleSidebar}
          />
        }
      </div>
      <div className='container max-w-4xl mb-6 px-4 md:px-2 pb-8 h-auto'>
        <div className='h-4 w-full mb-10'>
          <h2 className="text-xl font-bold mb-4 mt-6">默认模型</h2>
        </div>
        <DefaultChatModel workspaceId={workspaceId} />
        <ChatNaming workspaceId={workspaceId} />
        <div className='h-6'></div>
      </div>
    </div>
  )
}

export default Userpage
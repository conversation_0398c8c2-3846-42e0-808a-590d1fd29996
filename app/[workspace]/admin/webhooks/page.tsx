'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { Table, Tag, Card, Typography, Space, Button, message, Tooltip } from 'antd';
import { ReloadOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { useParams } from 'next/navigation';
import type { ColumnsType } from 'antd/es/table';

const { Title } = Typography;

interface WebhookEvent {
  id: string;
  eventId: string;
  eventType: string;
  status: 'pending' | 'processed' | 'failed';
  stripeSessionId?: string;
  orderId?: string;
  processedAt?: string;
  errorMessage?: string;
  retryCount: number;
  createdAt: string;
}

const WebhookEventsPage = () => {
  const params = useParams();
  const workspaceId = params.workspace as string;
  const [events, setEvents] = useState<WebhookEvent[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchEvents = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/${workspaceId}/webhook-events`);
      if (!response.ok) {
        throw new Error('Failed to fetch webhook events');
      }
      const data = await response.json();
      setEvents(data.events || []);
    } catch (error) {
      console.error('Error fetching webhook events:', error);
      message.error('获取 webhook 事件失败');
      // 设置空数组作为后备
      setEvents([]);
    } finally {
      setLoading(false);
    }
  }, [workspaceId]);

  useEffect(() => {
    fetchEvents();
  }, [fetchEvents]);

  const getStatusTag = (status: string) => {
    const statusConfig = {
      pending: { color: 'orange', text: '处理中' },
      processed: { color: 'green', text: '已处理' },
      failed: { color: 'red', text: '失败' },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const columns: ColumnsType<WebhookEvent> = [
    {
      title: '事件ID',
      dataIndex: 'eventId',
      key: 'eventId',
      width: 200,
      render: (text) => (
        <Tooltip title={text}>
          <code style={{ fontSize: '12px' }}>{text.substring(0, 20)}...</code>
        </Tooltip>
      ),
    },
    {
      title: '事件类型',
      dataIndex: 'eventType',
      key: 'eventType',
      width: 200,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: getStatusTag,
    },
    {
      title: 'Session ID',
      dataIndex: 'stripeSessionId',
      key: 'stripeSessionId',
      width: 150,
      render: (text) => text ? (
        <Tooltip title={text}>
          <code style={{ fontSize: '12px' }}>{text.substring(0, 15)}...</code>
        </Tooltip>
      ) : '-',
    },
    {
      title: '订单ID',
      dataIndex: 'orderId',
      key: 'orderId',
      width: 150,
      render: (text) => text ? (
        <Tooltip title={text}>
          <code style={{ fontSize: '12px' }}>{text.substring(0, 15)}...</code>
        </Tooltip>
      ) : '-',
    },
    {
      title: '重试次数',
      dataIndex: 'retryCount',
      key: 'retryCount',
      width: 80,
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (text) => new Date(text).toLocaleString('zh-CN'),
    },
    {
      title: '处理时间',
      dataIndex: 'processedAt',
      key: 'processedAt',
      width: 150,
      render: (text) => text ? new Date(text).toLocaleString('zh-CN') : '-',
    },
    {
      title: '错误信息',
      dataIndex: 'errorMessage',
      key: 'errorMessage',
      width: 200,
      render: (text) => text ? (
        <Tooltip title={text}>
          <span style={{ color: 'red', fontSize: '12px' }}>
            {text.substring(0, 30)}...
          </span>
        </Tooltip>
      ) : '-',
    },
  ];

  return (
    <div className="p-6">
      <Card>
        <div className="flex justify-between items-center mb-6">
          <Title level={3}>Webhook 事件监控</Title>
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={fetchEvents}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        </div>

        <div className="mb-4 p-4 bg-blue-50 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <InfoCircleOutlined className="text-blue-500" />
            <span className="font-medium">Webhook 事件说明</span>
          </div>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• <strong>checkout.session.completed</strong>: 支付完成事件，会自动处理订单和积分</li>
            <li>• <strong>payment_intent.succeeded</strong>: 支付成功事件</li>
            <li>• <strong>payment_intent.payment_failed</strong>: 支付失败事件</li>
          </ul>
        </div>

        <Table
          columns={columns}
          dataSource={events}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  );
};

export default WebhookEventsPage;

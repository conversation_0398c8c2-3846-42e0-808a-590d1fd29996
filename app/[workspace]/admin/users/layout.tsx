'use client';
import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname, useParams } from 'next/navigation';
import { Menu, MenuProps, Button } from 'antd';
import ToggleSidebar from "@/app/images/hideSidebar.svg";
import useAdminSidebarCollapsed from '@/app/store/adminSidebarCollapsed';
import { useTranslations } from 'next-intl';

const UserListPage = ({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) => {
  const t = useTranslations('Admin.Users');
  const pathname = usePathname();
  const params = useParams();
  const workspaceId = params.workspace as string;
  const [current, setCurrent] = useState('');
  const { isSidebarCollapsed, toggleSidebar } = useAdminSidebarCollapsed();

  // 构建多租户路由链接
  const items = [
    {
      label: <Link href={`/${workspaceId}/admin/users/list`}>{t('userList')}</Link>,
      key: 'list',
    },
    {
      label: <Link href={`/${workspaceId}/admin/users/group`}>{t('groupManagement')}</Link>,
      key: 'group',
    },
  ];

  const onClick: MenuProps['onClick'] = (e) => {
    setCurrent(e.key);
  };

  useEffect(() => {
    // 使用多租户路径模式检测当前活动菜单项
    if (pathname.endsWith('/admin/users/list')) {
      setCurrent('list');
    } else if (pathname.endsWith('/admin/users/group')) {
      setCurrent('group');
    }
  }, [pathname]);
  return (
    <div className='flex flex-col w-full items-center'>
      <div className='flex flex-row w-full items-center h-10 px-1'>
        {isSidebarCollapsed &&
          <Button
            icon={<ToggleSidebar style={{ 'color':'#999','fontSize': '20px', 'verticalAlign': 'middle' }} />}
            type='text'
            onClick={toggleSidebar}
          />
        }
      </div>
      <div className='container mb-6 px-8'>
        <Menu onClick={onClick} selectedKeys={[current]} mode="horizontal" items={items} />
        {children}
      </div>
    </div>
  );
};

export default UserListPage;
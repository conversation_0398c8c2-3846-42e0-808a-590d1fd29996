'use server';
import { groups, users, userWorkspace } from '@/app/db/schema';
import { db } from '@/app/db';
import { desc, eq, and, isNull } from 'drizzle-orm';
import { isWorkspaceAdmin } from '@/app/utils/workspace';
import bcrypt from "bcryptjs";

type UserActionParams = {
  email: string;
  isAdmin: boolean;
  groupId?: string;
};

const handleDatabaseError = (error: unknown, defaultMessage: string) => ({
  success: false,
  message: error instanceof Error ? error.message : defaultMessage
});

const hashPassword = async (password: string) => {
  const salt = await bcrypt.genSalt(10);
  return bcrypt.hash(password, salt);
};

// 用户注册后自动关联到待定的workspace记录
export async function linkUserToPendingWorkspaces(userId: string, email: string) {
  try {
    // 查找所有待定的workspace记录（userId为null且email匹配）
    const pendingWorkspaces = await db.query.userWorkspace.findMany({
      where: and(
        isNull(userWorkspace.userId),
        eq(userWorkspace.email, email),
        eq(userWorkspace.isActive, true)
      )
    });

    if (pendingWorkspaces.length === 0) {
      return { success: true, message: 'No pending workspaces found' };
    }

    // 更新所有匹配的记录，将userId关联上
    for (const pendingWorkspace of pendingWorkspaces) {
      await db.update(userWorkspace)
        .set({ userId: userId })
        .where(eq(userWorkspace.id, pendingWorkspace.id));
    }

    return {
      success: true,
      message: `Linked user to ${pendingWorkspaces.length} workspace(s)`
    };
  } catch (error) {
    console.error('Error linking user to pending workspaces:', error);
    return {
      success: false,
      message: 'Failed to link user to pending workspaces'
    };
  }
}

export async function getUserList(workspaceId: string, groupId?: string) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    throw new Error('Access denied');
  }

  try {
    // 构建查询条件
    let whereConditions = [
      eq(userWorkspace.workspaceId, workspaceId),
      eq(userWorkspace.isActive, true)
    ];

    // 如果指定了分组，添加分组过滤条件
    if (groupId && groupId !== '_all') {
      whereConditions.push(eq(userWorkspace.groupId, groupId));
    }

    // 查询用户工作空间关联信息，包含用户基本信息和用量数据
    const userWorkspaceList = await db.query.userWorkspace.findMany({
      where: and(...whereConditions),
      columns: {
        id: true,
        userId: true,
        email: true, // 新增email字段用于待定用户
        groupId: true,
        role: true,
        todayTotalTokens: true,
        currentMonthTotalTokens: true,
        usageUpdatedAt: true,
        joinedAt: true,
      },
      with: {
        user: {
          columns: {
            id: true,
            name: true,
            email: true,
            createdAt: true,
          }
        },
        group: {
          columns: {
            id: true,
            name: true,
            tokenLimitType: true,
            monthlyTokenLimit: true,
          }
        }
      },
      orderBy: [desc(userWorkspace.joinedAt)]
    });

    if (userWorkspaceList.length === 0) {
      return [];
    }

    // 获取今天凌晨 0 点的时间戳
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);

    // 处理每条记录，计算实际用量，支持待定用户
    const result = userWorkspaceList.map(userWorkspaceItem => {
      const isPendingUser = !userWorkspaceItem.userId; // 判断是否为待定用户

      return {
        id: userWorkspaceItem.id, // 使用 userWorkspace 的 id 作为唯一标识
        userId: userWorkspaceItem.userId,
        name: isPendingUser ? null : userWorkspaceItem.user?.name,
        email: isPendingUser ? userWorkspaceItem.email : userWorkspaceItem.user?.email,
        role: userWorkspaceItem.role,
        createdAt: isPendingUser ? userWorkspaceItem.joinedAt : userWorkspaceItem.user?.createdAt,
        groupId: userWorkspaceItem.groupId,
        group: userWorkspaceItem.group,
        isPending: isPendingUser, // 新增字段标识是否为待定用户
        todayTotalTokens: userWorkspaceItem.usageUpdatedAt && new Date(userWorkspaceItem.usageUpdatedAt) >= today
          ? userWorkspaceItem.todayTotalTokens
          : 0,
        currentMonthTotalTokens: userWorkspaceItem.usageUpdatedAt && new Date(userWorkspaceItem.usageUpdatedAt) >= firstDayOfMonth
          ? userWorkspaceItem.currentMonthTotalTokens
          : 0,
        usageUpdatedAt: userWorkspaceItem.usageUpdatedAt,
      };
    });

    return result;
  } catch (error) {
    throw new Error('Failed to fetch user list');
  }
}

// 智能用户添加函数 - 支持已注册和未注册用户
export async function addUser(workspaceId: string, user: UserActionParams) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return handleDatabaseError(null, 'Access denied');
  }

  try {
    // 验证分组是否属于当前workspace
    const group = user.groupId ? await db.query.groups.findFirst({
      where: and(eq(groups.id, user.groupId), eq(groups.workspaceId, workspaceId))
    }) : null;
    if (user.groupId && !group) {
      return {
        success: false,
        message: 'Group not found in this workspace'
      }
    }

    // 检查用户是否已在当前workspace中
    const existingUserWorkspace = await db.query.userWorkspace.findFirst({
      where: and(
        eq(userWorkspace.workspaceId, workspaceId),
        eq(userWorkspace.email, user.email),
        eq(userWorkspace.isActive, true)
      )
    });

    if (existingUserWorkspace) {
      return { success: false, message: 'User already exists in this workspace' }
    }

    // 检查用户是否已注册
    const existingUser = await db.query.users.findFirst({
      where: eq(users.email, user.email),
    });

    if (existingUser) {
      // 已注册用户：直接添加到workspace
      await db.insert(userWorkspace).values({
        userId: existingUser.id,
        workspaceId: workspaceId,
        email: user.email,
        role: user.isAdmin ? 'admin' : 'member',
        groupId: user.groupId,
        isActive: true
      });

      return {
        success: true,
        message: 'Registered user added to workspace successfully'
      }
    } else {
      // 未注册用户：创建待定记录
      await db.insert(userWorkspace).values({
        userId: null, // 待定状态
        workspaceId: workspaceId,
        email: user.email,
        role: user.isAdmin ? 'admin' : 'member',
        groupId: user.groupId,
        isActive: true
      });

      return {
        success: true,
        message: 'Pending user invitation created successfully'
      }
    }
  } catch (error) {
    return handleDatabaseError(error, 'Failed to add user');
  }
}

// 保留原有的创建用户函数（用于其他地方可能需要的完整用户创建）
export async function createUserWithPassword(workspaceId: string, user: UserActionParams & { password: string }) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return handleDatabaseError(null, 'Access denied');
  }
  try {
    const emailExists = await db.query.users.findFirst({
      where: eq(users.email, user.email),
    });
    if (emailExists) return { success: false, message: 'Email has been registered' }
    const hashedPassword = await hashPassword(user.password);

    // 验证分组是否属于当前workspace
    const group = user.groupId ? await db.query.groups.findFirst({
      where: and(eq(groups.id, user.groupId), eq(groups.workspaceId, workspaceId))
    }) : null;
    if (user.groupId && !group) {
      return {
        success: false,
        message: 'Group not found in this workspace'
      }
    }

    // 创建用户
    const [newUser] = await db.insert(users).values({
      email: user.email,
      password: hashedPassword,
      isAdmin: user.isAdmin,
    }).returning();

    // 将用户添加到workspace，并设置分组
    await db.insert(userWorkspace).values({
      userId: newUser.id,
      workspaceId: workspaceId,
      email: user.email,
      role: user.isAdmin ? 'admin' : 'member',
      groupId: user.groupId,
      isActive: true
    });

    return {
      success: true,
      message: 'User created successfully'
    }
  } catch (error) {
    return handleDatabaseError(error, 'User registration failed');
  }
}

export async function deleteUser(workspaceId: string, userWorkspaceId: string) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    throw new Error('Access denied');
  }
  try {
    // 验证 userWorkspace 记录是否存在且属于当前workspace
    const userWorkspaceRecord = await db.query.userWorkspace.findFirst({
      where: and(
        eq(userWorkspace.id, userWorkspaceId),
        eq(userWorkspace.workspaceId, workspaceId),
        eq(userWorkspace.isActive, true)
      )
    });

    if (!userWorkspaceRecord) {
      return {
        success: false,
        message: 'User not found in this workspace'
      }
    }

    // 从workspace中移除用户（软删除）
    await db.update(userWorkspace).set({
      isActive: false
    }).where(eq(userWorkspace.id, userWorkspaceId));

    return {
      success: true,
      message: 'User removed from workspace successfully'
    }
  } catch (error) {
    return {
      success: false,
      message: 'User delete failed'
    }
  }
}

export async function updateUser(workspaceId: string, userWorkspaceId: string, user: UserActionParams) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return handleDatabaseError(null, 'Access denied');
  }
  try {
    // 验证 userWorkspace 记录是否存在且属于当前workspace
    const userWorkspaceRecord = await db.query.userWorkspace.findFirst({
      where: and(
        eq(userWorkspace.id, userWorkspaceId),
        eq(userWorkspace.workspaceId, workspaceId),
        eq(userWorkspace.isActive, true)
      )
    });

    if (!userWorkspaceRecord) {
      return {
        success: false,
        message: 'User not found in this workspace'
      }
    }

    // 验证分组是否属于当前workspace
    if (user.groupId) {
      const group = await db.query.groups.findFirst({
        where: and(eq(groups.id, user.groupId), eq(groups.workspaceId, workspaceId))
      });
      if (!group) {
        return {
          success: false,
          message: 'Group not found in this workspace'
        }
      }
    }

    // 更新workspace中的角色和分组
    let newRole = 'member';
    if(userWorkspaceRecord.role === 'owner') {
      newRole = 'owner';
    } else {
      newRole = user.isAdmin ? 'admin' : 'member';
    }

    await db.update(userWorkspace).set({
      role: newRole as "admin" | "member" | "owner",
      groupId: user.groupId
    }).where(eq(userWorkspace.id, userWorkspaceId));

    return {
      success: true,
      message: 'User updated successfully'
    }
  } catch (error) {
    return handleDatabaseError(error, 'User update failed');
  }
}
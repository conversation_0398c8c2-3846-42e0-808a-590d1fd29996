'use server';
import { users } from '@/app/db/schema';
import { db } from '@/app/db';
import { desc, eq } from 'drizzle-orm';
import { isWorkspaceAdmin, createErrorResponse } from '@/app/utils/workspace';
import bcrypt from "bcryptjs";

export async function getUserList(workspaceId: string) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    throw new Error('Access denied');
  }

  try {
    const result = await db.query.users.findMany({
      orderBy: [desc(users.createdAt)],
    });
    return result;
  } catch (error) {
    throw new Error('query user list fail');
  }
}

export async function addUser(workspaceId: string, userBasicInfo: { email: string, password: string, isAdmin: boolean }) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return createErrorResponse('Access denied');
  }

  try {
    const existingUser = await db.query.users.findFirst({
      where: eq(users.email, userBasicInfo.email),
    });

    if (existingUser) {
      return {
        success: false,
        message: '邮箱已被注册',
      }
    }

    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(userBasicInfo.password, salt);

    await db.insert(users).values({
      email: userBasicInfo.email,
      password: hashedPassword,
      isAdmin: userBasicInfo.isAdmin,
    });
    return {
      success: true,
    }
  } catch (error) {
    return {
      success: false,
      message: 'database add error'
    }
  }
}

export async function deleteUser(workspaceId: string, email: string) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return createErrorResponse('Access denied');
  }

  try {
    await db.delete(users).where(eq(users.email, email));
    return {
      success: true,
      message: '用户删除成功'
    }
  } catch (error) {
    return {
      success: false,
      message: 'database delete error'
    }
  }
}

export async function updateUser(workspaceId: string, email: string, userBasicInfo: { email: string, password?: string, isAdmin: boolean }) {
  // 验证workspace管理员权限
  const adminValidation = await isWorkspaceAdmin(workspaceId);
  if (!adminValidation) {
    return createErrorResponse('Access denied');
  }

  try {
    const existingUser = await db.query.users.findFirst({
      where: eq(users.email, email),
    });

    if (!existingUser) {
      return {
        success: false,
        message: '该用户不存在',
      };
    }

    if (userBasicInfo.password) {
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(userBasicInfo.password, salt);
      // 更新用户信息
      await db.update(users)
        .set({
          email: userBasicInfo.email,
          password: hashedPassword,
          isAdmin: userBasicInfo.isAdmin,
        })
        .where(eq(users.email, email));
    } else {
      await db.update(users)
        .set({
          email: userBasicInfo.email,
          isAdmin: userBasicInfo.isAdmin,
        })
        .where(eq(users.email, email));
    }
    return {
      success: true,
      message: '用户信息已更新',
    };
  } catch (error) {
    return {
      success: false,
      message: 'database update error'
    }
  }
}
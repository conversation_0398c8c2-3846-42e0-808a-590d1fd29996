'use server';
import { db } from '@/app/db';
import { workspaceInvites } from '@/app/db/schema';
import { eq, and } from 'drizzle-orm';
import { isWorkspaceAdmin, createErrorResponse, createSuccessResponse } from '@/app/utils/workspace';
import { customAlphabet } from 'nanoid';

// 生成邀请码，使用字母和数字，长度为12位
const generateInviteCode = customAlphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789', 12);

/**
 * 获取当前工作空间的邀请码
 */
export async function getWorkspaceInvite(workspaceId: string) {
  try {
    // 验证管理员权限
    const adminValidation = await isWorkspaceAdmin(workspaceId);
    if (!adminValidation) {
      return createErrorResponse('Access denied');
    }

    // 查找当前活跃的邀请码
    const invite = await db.query.workspaceInvites.findFirst({
      where: and(
        eq(workspaceInvites.workspaceId, workspaceId),
        eq(workspaceInvites.isActive, true)
      )
    });

    if (!invite) {
      return createErrorResponse('No active invite found');
    }

    // 检查是否过期
    const now = new Date();
    if (invite.expiresAt < now) {
      // 如果过期，将其设为非活跃状态
      await db.update(workspaceInvites)
        .set({ 
          isActive: false,
          updatedAt: new Date()
        })
        .where(eq(workspaceInvites.id, invite.id));
      
      return createErrorResponse('Invite expired');
    }

    return createSuccessResponse({
      inviteCode: invite.inviteCode,
      expiresAt: invite.expiresAt,
      createdAt: invite.createdAt
    });
  } catch (error) {
    console.error('Get workspace invite error:', error);
    return createErrorResponse('Failed to get invite');
  }
}

/**
 * 创建或刷新工作空间邀请码
 */
export async function createOrRefreshInvite(workspaceId: string) {
  try {
    // 验证管理员权限
    const adminValidation = await isWorkspaceAdmin(workspaceId);
    if (!adminValidation) {
      return createErrorResponse('Access denied');
    }

    // 获取当前用户ID
    const { auth } = await import('@/auth');
    const session = await auth();
    if (!session?.user?.id) {
      return createErrorResponse('User not authenticated');
    }

    const userId = session.user.id;
    const now = new Date();
    const expiresAt = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000); // 7天后过期

    // 先将现有的活跃邀请码设为非活跃状态
    await db.update(workspaceInvites)
      .set({ 
        isActive: false,
        updatedAt: now
      })
      .where(and(
        eq(workspaceInvites.workspaceId, workspaceId),
        eq(workspaceInvites.isActive, true)
      ));

    // 生成新的邀请码
    const inviteCode = generateInviteCode();

    // 创建新的邀请记录
    const newInvite = await db.insert(workspaceInvites)
      .values({
        workspaceId,
        inviteCode,
        createdBy: userId,
        expiresAt,
        isActive: true,
        createdAt: now,
        updatedAt: now
      })
      .returning();

    return createSuccessResponse({
      inviteCode: newInvite[0].inviteCode,
      expiresAt: newInvite[0].expiresAt,
      createdAt: newInvite[0].createdAt
    });
  } catch (error) {
    console.error('Create or refresh invite error:', error);
    return createErrorResponse('Failed to create invite');
  }
}

/**
 * 验证邀请码是否有效
 */
export async function validateInviteCode(workspaceId: string, inviteCode: string) {
  try {
    const invite = await db.query.workspaceInvites.findFirst({
      where: and(
        eq(workspaceInvites.workspaceId, workspaceId),
        eq(workspaceInvites.inviteCode, inviteCode),
        eq(workspaceInvites.isActive, true)
      )
    });

    if (!invite) {
      return { valid: false, message: 'Invalid invite code' };
    }

    // 检查是否过期
    const now = new Date();
    if (invite.expiresAt < now) {
      // 如果过期，将其设为非活跃状态
      await db.update(workspaceInvites)
        .set({ 
          isActive: false,
          updatedAt: new Date()
        })
        .where(eq(workspaceInvites.id, invite.id));
      
      return { valid: false, message: 'Invite code expired' };
    }

    return { valid: true, message: 'Valid invite code' };
  } catch (error) {
    console.error('Validate invite code error:', error);
    return { valid: false, message: 'Failed to validate invite code' };
  }
}

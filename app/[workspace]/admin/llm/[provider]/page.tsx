'use client';
import React from 'react';
import { useParams } from 'next/navigation'
import Settings from "@/app/components/admin/llm/Settings";
import HiveChatSettings from '@/app/components/admin/llm/HiveChatSettings';

const ProviderSettingPage = () => {
  const params = useParams();
  const provider = params.provider as string;
  return provider === 'hivechat' ? <HiveChatSettings providerId={provider}></HiveChatSettings> : <Settings providerId={provider} />;
}

export default ProviderSettingPage
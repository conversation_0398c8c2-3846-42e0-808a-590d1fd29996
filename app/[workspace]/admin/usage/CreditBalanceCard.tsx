'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, Statistic, Spin, Alert, Button } from 'antd';
import Link from 'next/link'
import { getUserCreditBalance, UserCreditBalanceResponse } from './actions';

interface CreditBalanceCardProps {
  workspaceId: string;
}

const CreditBalanceCard: React.FC<CreditBalanceCardProps> = ({ workspaceId }) => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<UserCreditBalanceResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  // 加载积分余额数据
  const loadCreditBalance = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await getUserCreditBalance(workspaceId);
      setData(result);

      if (result.status === 'error') {
        setError(result.message || '获取积分余额失败');
      }
    } catch (err) {
      setError('获取积分余额时发生错误');
      console.error('Error loading credit balance:', err);
    } finally {
      setLoading(false);
    }
  }, [workspaceId]);

  // 初始加载
  useEffect(() => {
    if (workspaceId) {
      loadCreditBalance();
    }
  }, [workspaceId, loadCreditBalance]);

  if (loading) {
    return (
      <Card className="mb-6">
        <div className="flex items-center justify-center py-8">
          <Spin size="large" />
          <span className="ml-3 text-gray-600">加载积分余额中...</span>
        </div>
      </Card>
    );
  }

  if (error || !data || data.status === 'error') {
    return (
      <Card className="mb-6">
        <Alert
          message="积分余额加载失败"
          description={error || data?.message || '未知错误'}
          type="error"
          showIcon
        />
      </Card>
    );
  }

  const { creditBalance, formattedBalance } = data.data!;

  return (
    <Card className="mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-3">
            <div>
              <Statistic
                title="当前积分余额"
                value={formattedBalance}
                valueStyle={{
                  color: creditBalance > 0 ? '#3f8600' : '#cf1322',
                  fontSize: '24px',
                  fontWeight: 'bold'
                }}
                suffix="积分"
              />
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Link href='./plans'>
            <Button>
              去充值
            </Button>
          </Link>
        </div>
      </div>
    </Card>
  );
};

export default CreditBalanceCard;

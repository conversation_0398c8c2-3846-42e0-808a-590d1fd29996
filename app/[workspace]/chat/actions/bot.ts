'use server';
import { db } from '@/app/db';
import { eq, and, or, desc } from 'drizzle-orm'
import { chats, bots } from '@/app/db/schema';
import { getSessionAndValidateWorkspace, createErrorResponse, createSuccessResponse } from '@/app/utils/workspace';

export const addBotInServer = async (
  workspaceId: string,
  botInfo: {
    title: string;
    desc?: string;
    prompt: string;
    avatar: string;
    avatarType: 'emoji' | 'url';
  }
) => {
  // 验证用户权限和 workspace 访问
  const validation = await getSessionAndValidateWorkspace(workspaceId);
  if (!validation.hasAccess) {
    return createErrorResponse(validation.error || 'Access denied');
  }

  try {
    const botResult = await db.insert(bots)
      .values({
        ...botInfo,
        creator: validation.userId!,
        workspaceId
      })
      .returning();

    return createSuccessResponse(botResult[0]);
  } catch (error) {
    console.error('Add bot error:', error);
    return createErrorResponse('Failed to create bot');
  }
}

export const deleteBotInServer = async (workspaceId: string, botId: number) => {
  // 验证用户权限和 workspace 访问
  const validation = await getSessionAndValidateWorkspace(workspaceId);
  if (!validation.hasAccess) {
    return createErrorResponse(validation.error || 'Access denied');
  }

  try {
    // 只允许删除用户自己创建的 bot，且必须在指定的 workspace 中
    const result = await db.delete(bots)
      .where(
        and(
          eq(bots.id, botId),
          eq(bots.creator, validation.userId!),
          eq(bots.workspaceId, workspaceId)
        )
      )
      .returning();

    if (result.length === 0) {
      return createErrorResponse('Bot not found or you do not have permission to delete it');
    }

    return createSuccessResponse();
  } catch (error) {
    console.error('Delete bot error:', error);
    return createErrorResponse('Failed to delete bot');
  }
}

export const addBotToChatInServer = async (workspaceId: string, botId: number) => {
  // 验证用户权限和 workspace 访问
  const validation = await getSessionAndValidateWorkspace(workspaceId);
  if (!validation.hasAccess) {
    return createErrorResponse(validation.error || 'Access denied');
  }

  try {
    // 查找指定的 bot，确保它属于当前 workspace
    const result = await db.select()
      .from(bots)
      .where(
        and(
          eq(bots.id, botId),
          eq(bots.workspaceId, workspaceId)
        )
      );

    if (result.length === 0) {
      return createErrorResponse('Bot not found in this workspace');
    }

    const botInfo = result[0];
    const safeTitle = botInfo.title.length > 255 ? botInfo.title.slice(0, 255) : botInfo.title;

    const chatResult = await db.insert(chats)
      .values({
        title: safeTitle,
        botId: botInfo.id,
        avatar: botInfo.avatar,
        avatarType: botInfo.avatarType,
        isWithBot: true,
        prompt: botInfo.prompt,
        userId: validation.userId!,
        workspaceId
      })
      .returning();

    return createSuccessResponse(chatResult[0]);
  } catch (error) {
    console.error('Add bot to chat error:', error);
    return createErrorResponse('Failed to create chat with bot');
  }
}

export const getBotListInServer = async (workspaceId: string) => {
  // 验证用户权限和 workspace 访问
  const validation = await getSessionAndValidateWorkspace(workspaceId);
  if (!validation.hasAccess) {
    return {
      status: 'fail',
      data: [],
      message: validation.error || 'Access denied'
    };
  }

  try {
    const result = await db.select()
      .from(bots)
      .where(
        and(
          eq(bots.workspaceId, workspaceId),
          or(
            eq(bots.creator, validation.userId!),
            eq(bots.creator, 'public')
          )
        )
      )
      .orderBy(desc(bots.createdAt));

    return {
      status: 'success',
      data: result
    };
  } catch (error) {
    console.error('Get bot list error:', error);
    return {
      status: 'fail',
      data: [],
      message: 'Failed to get bot list'
    };
  }
}

export const getBotInfoInServer = async (workspaceId: string, botId: number) => {
  // 验证用户权限和 workspace 访问
  const validation = await getSessionAndValidateWorkspace(workspaceId);
  if (!validation.hasAccess) {
    return createErrorResponse(validation.error || 'Access denied');
  }

  try {
    const result = await db.select()
      .from(bots)
      .where(
        and(
          eq(bots.id, botId),
          eq(bots.workspaceId, workspaceId)
        )
      );

    if (result.length === 0) {
      return createErrorResponse('Bot not found in this workspace');
    }

    return createSuccessResponse(result[0]);
  } catch (error) {
    console.error('Get bot info error:', error);
    return createErrorResponse('Failed to get bot info');
  }
}
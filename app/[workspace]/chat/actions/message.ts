'use server';
import { db } from '@/app/db';
import { MCPToolResponse, MessageContent } from '@/types/llm';
import { eq, and, asc } from 'drizzle-orm';
import { messages } from '@/app/db/schema';
import { searchResultType, WebSearchResponse } from '@/types/search';
import { getSessionAndValidateWorkspace, createErrorResponse, createSuccessResponse } from '@/app/utils/workspace';

export const clearMessageInServer = async (workspaceId: string, chatId: string) => {
  // 验证用户权限和 workspace 访问
  const validation = await getSessionAndValidateWorkspace(workspaceId);
  if (!validation.hasAccess) {
    return createErrorResponse(validation.error || 'Access denied');
  }

  try {
    const result = await db.delete(messages)
      .where(
        and(
          eq(messages.chatId, chatId),
          eq(messages.userId, validation.userId!),
          eq(messages.workspaceId, workspaceId)
        )
      )
      .returning();

    return createSuccessResponse({ deletedCount: result.length });
  } catch (error) {
    console.error('Clear messages error:', error);
    return createErrorResponse('Failed to clear messages');
  }
}
export const deleteMessageInServer = async (workspaceId: string, messageId: number) => {
  // 验证用户权限和 workspace 访问
  const validation = await getSessionAndValidateWorkspace(workspaceId);
  if (!validation.hasAccess) {
    return createErrorResponse(validation.error || 'Access denied');
  }

  try {
    const result = await db.delete(messages)
      .where(
        and(
          eq(messages.id, messageId),
          eq(messages.userId, validation.userId!),
          eq(messages.workspaceId, workspaceId)
        )
      )
      .returning();

    if (result.length === 0) {
      return createErrorResponse('Message not found or you do not have permission to delete it');
    }

    return createSuccessResponse();
  } catch (error) {
    console.error('Delete message error:', error);
    return createErrorResponse('Failed to delete message');
  }
}

export const getMessagesInServer = async (workspaceId: string, chatId: string) => {
  // 验证用户权限和 workspace 访问
  const validation = await getSessionAndValidateWorkspace(workspaceId);
  if (!validation.hasAccess) {
    return {
      status: 'fail',
      data: [],
      message: validation.error || 'Access denied'
    };
  }

  try {
    const result = await db.select()
      .from(messages)
      .where(
        and(
          eq(messages.chatId, chatId),
          eq(messages.userId, validation.userId!),
          eq(messages.workspaceId, workspaceId)
        )
      )
      .orderBy(asc(messages.createdAt));

    return {
      status: 'success',
      data: result
    };
  } catch (error) {
    console.error('Get messages error:', error);
    return {
      status: 'fail',
      data: [],
      message: 'Failed to get messages'
    };
  }
}

export const addMessageInServer = async (
  workspaceId: string,
  message: {
    chatId: string,
    role: string,
    content: MessageContent,
    reasoninContent?: string,
    searchEnabled?: boolean,
    searchStatus?: searchResultType,
    mcpTools?: MCPToolResponse[],
    providerId: string,
    model: string,
    type: 'text' | 'image' | 'error' | 'break' | 'search',
    inputTokens?: number | null,
    outputTokens?: number | null,
    totalTokens?: number | null,
    errorType?: string,
    errorMessage?: string,
  }
) => {
  // 验证用户权限和 workspace 访问
  const validation = await getSessionAndValidateWorkspace(workspaceId);
  if (!validation.hasAccess) {
    return {
      status: 'fail',
      message: validation.error || 'Access denied'
    };
  }

  try {
    const [result] = await db.insert(messages)
      .values({
        userId: validation.userId!,
        workspaceId,
        ...message
      })
      .returning();

    return result.id;
  } catch (error) {
    console.error('Add message error:', error);
    return {
      status: 'fail',
      message: 'Failed to add message'
    };
  }
}

export const updateMessageInServer = async (
  workspaceId: string,
  messageId: number,
  newMessageInfo: {
    content?: MessageContent;
    reasoninContent?: string;
    searchEnabled?: boolean;
    searchStatus?: searchResultType;
    mcpTools?: MCPToolResponse[];
    inputTokens?: number | null;
    outputTokens?: number | null;
    totalTokens?: number | null;
    errorType?: string;
    errorMessage?: string;
  }
) => {
  // 验证用户权限和 workspace 访问
  const validation = await getSessionAndValidateWorkspace(workspaceId);
  if (!validation.hasAccess) {
    return {
      status: 'fail',
      message: validation.error || 'Access denied'
    };
  }

  try {
    const result = await db.update(messages)
      .set({
        ...newMessageInfo,
        updatedAt: new Date()
      })
      .where(
        and(
          eq(messages.id, messageId),
          eq(messages.userId, validation.userId!),
          eq(messages.workspaceId, workspaceId)
        )
      )
      .returning();

    if (result.length === 0) {
      return {
        status: 'fail',
        message: 'Message not found or you do not have permission to update it'
      };
    }

    return {
      status: 'success',
      message: 'Message updated successfully'
    };
  } catch (error) {
    console.error('Update message error:', error);
    return {
      status: 'fail',
      message: 'Failed to update message'
    };
  }
}

export const updateMessageWebSearchInServer = async (
  workspaceId: string,
  messageId: number,
  searchEnabled: boolean,
  searchStatus: "none" | "searching" | "error" | "done",
  webSearch?: WebSearchResponse,
) => {
  // 验证用户权限和 workspace 访问
  const validation = await getSessionAndValidateWorkspace(workspaceId);
  if (!validation.hasAccess) {
    return {
      status: 'fail',
      message: validation.error || 'Access denied'
    };
  }

  try {
    const result = await db.update(messages)
      .set({
        searchEnabled: searchEnabled,
        searchStatus: searchStatus,
        webSearch: webSearch,
        updatedAt: new Date()
      })
      .where(
        and(
          eq(messages.id, messageId),
          eq(messages.userId, validation.userId!),
          eq(messages.workspaceId, workspaceId)
        )
      )
      .returning();

    if (result.length === 0) {
      return {
        status: 'fail',
        message: 'Message not found or you do not have permission to update it'
      };
    }

    return {
      status: 'success',
      message: '搜索信息已保存'
    };
  } catch (error) {
    console.error('同步搜索响应失败:', error);
    return {
      status: 'fail',
      message: '同步搜索失败'
    };
  }
}
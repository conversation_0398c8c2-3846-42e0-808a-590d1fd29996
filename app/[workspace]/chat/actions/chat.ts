'use server';
import { db } from '@/app/db';
import { eq, and, desc, asc, inArray } from 'drizzle-orm';
import { ChatType, MCPToolResponse } from '@/types/llm';
import WebSearchService from '@/app/services/WebSearchService';
import { chats, messages, appSettings, mcpServers, mcpTools, searchEngineConfig, llmSettingsTable } from '@/app/db/schema';
import { WebSearchResponse } from '@/types/search';
import { getSessionAndValidateWorkspace, createErrorResponse, createSuccessResponse } from '@/app/utils/workspace';

export const addChatInServer = async (
  workspaceId: string,
  chatInfo: {
    title: string;
    defaultModel?: string;
    defaultProvider?: string;
    searchEnabled?: boolean;
    historyType?: 'all' | 'none' | 'count';
    historyCount?: number;
    isStar?: boolean;
    isWithBot?: boolean;
    botId?: number;
    avatar?: string;
    avatarType?: 'emoji' | 'url' | 'none';
    prompt?: string;
  }
) => {
  // 验证用户权限和 workspace 访问
  const validation = await getSessionAndValidateWorkspace(workspaceId);
  if (!validation.hasAccess) {
    return createErrorResponse(validation.error || 'Access denied');
  }

  try {
    const safeTitle = chatInfo.title.length > 255 ? chatInfo.title.slice(0, 255) : chatInfo.title;
    const result = await db.insert(chats)
      .values({
        ...chatInfo,
        title: safeTitle,
        userId: validation.userId!,
        workspaceId
      })
      .returning();

    return createSuccessResponse(result[0]);
  } catch (error) {
    console.error('Add chat error:', error);
    return createErrorResponse('Failed to create chat');
  }
}

export const getChatInfoInServer = async (workspaceId: string, chatId: string): Promise<{ status: string; data: ChatType | null }> => {
  // 验证用户权限和 workspace 访问
  const validation = await getSessionAndValidateWorkspace(workspaceId);
  if (!validation.hasAccess) {
    return {
      status: 'fail',
      data: null
    };
  }

  try {
    const result = await db.select()
      .from(chats)
      .where(
        and(
          eq(chats.id, chatId),
          eq(chats.userId, validation.userId!),
          eq(chats.workspaceId, workspaceId)
        )
      );

    if (result.length === 0) {
      return {
        status: 'fail',
        data: null
      };
    }

    const data = result[0];
    return {
      status: 'success',
      data: {
        id: data.id,
        title: data.title ?? undefined,
        defaultModel: data.defaultModel ?? undefined,
        defaultProvider: data.defaultProvider ?? undefined,
        searchEnabled: data.searchEnabled ?? undefined,
        historyType: data.historyType ?? undefined,
        historyCount: data.historyCount ?? undefined,
        isStar: data.isStar ?? undefined,
        isWithBot: data.isWithBot ?? undefined,
        botId: data.botId ?? undefined,
        avatarType: data.avatarType ?? undefined,
        prompt: data.prompt ?? undefined,
        createdAt: data.createdAt!,
        starAt: data.starAt ?? undefined,
      }
    };
  } catch (error) {
    console.error('Get chat info error:', error);
    return {
      status: 'fail',
      data: null
    };
  }
}

export const getChatListInServer = async (workspaceId: string) => {
  // 验证用户权限和 workspace 访问
  const validation = await getSessionAndValidateWorkspace(workspaceId);
  if (!validation.hasAccess) {
    return {
      status: 'fail',
      data: [],
      message: validation.error || 'Access denied'
    };
  }

  try {
    const result = await db.select()
      .from(chats)
      .where(
        and(
          eq(chats.userId, validation.userId!),
          eq(chats.workspaceId, workspaceId)
        )
      )
      .orderBy(desc(chats.createdAt));

    return {
      status: 'success',
      data: result
    };
  } catch (error) {
    console.error('Get chat list error:', error);
    return {
      status: 'fail',
      data: [],
      message: 'Failed to get chat list'
    };
  }
}

export const updateChatInServer = async (workspaceId: string, chatId: string, newChatInfo: {
  title?: string;
  defaultModel?: string;
  defaultProvider?: string;
  historyType?: 'all' | 'none' | 'count';
  historyCount?: number;
  isStar?: boolean;
  isWithBot?: boolean;
  botId?: number;
  avatar?: string;
  avatarType?: 'emoji' | 'url' | 'none';
  prompt?: string;
  starAt?: Date;
}) => {
  // 验证用户权限和 workspace 访问
  const validation = await getSessionAndValidateWorkspace(workspaceId);
  if (!validation.hasAccess) {
    return createErrorResponse(validation.error || 'Access denied');
  }

  try {
    const safeChatInfo = { ...newChatInfo };
    if (safeChatInfo.title && safeChatInfo.title.length > 255) {
      safeChatInfo.title = safeChatInfo.title.slice(0, 255);
    }

    const result = await db.update(chats)
      .set(safeChatInfo)
      .where(
        and(
          eq(chats.id, chatId),
          eq(chats.userId, validation.userId!),
          eq(chats.workspaceId, workspaceId)
        )
      )
      .returning();

    if (result.length === 0) {
      return createErrorResponse('Chat not found or you do not have permission to update it');
    }

    return createSuccessResponse();
  } catch (error) {
    console.error('Update chat error:', error);
    return createErrorResponse('Failed to update chat');
  }
}

export const updateChatTitleInServer = async (workspaceId: string, chatId: string, newTitle: string) => {
  // 验证用户权限和 workspace 访问
  const validation = await getSessionAndValidateWorkspace(workspaceId);
  if (!validation.hasAccess) {
    return createErrorResponse(validation.error || 'Access denied');
  }

  try {
    const safeTitle = newTitle.length > 255 ? newTitle.slice(0, 255) : newTitle;
    const result = await db.update(chats)
      .set({
        title: safeTitle,
      })
      .where(
        and(
          eq(chats.id, chatId),
          eq(chats.userId, validation.userId!),
          eq(chats.workspaceId, workspaceId)
        )
      )
      .returning();

    if (result.length === 0) {
      return createErrorResponse('Chat not found or you do not have permission to update it');
    }

    return createSuccessResponse();
  } catch (error) {
    console.error('Update chat title error:', error);
    return createErrorResponse('Failed to update chat title');
  }
}

export const deleteChatInServer = async (workspaceId: string, chatId: string) => {
  // 验证用户权限和 workspace 访问
  const validation = await getSessionAndValidateWorkspace(workspaceId);
  if (!validation.hasAccess) {
    return createErrorResponse(validation.error || 'Access denied');
  }

  try {
    // 删除聊天记录，确保只删除属于当前用户和 workspace 的数据
    const chatResult = await db.delete(chats)
      .where(
        and(
          eq(chats.id, chatId),
          eq(chats.userId, validation.userId!),
          eq(chats.workspaceId, workspaceId)
        )
      )
      .returning();

    if (chatResult.length === 0) {
      return createErrorResponse('Chat not found or you do not have permission to delete it');
    }

    // 删除相关的消息记录
    await db.delete(messages)
      .where(
        and(
          eq(messages.chatId, chatId),
          eq(messages.userId, validation.userId!),
          eq(messages.workspaceId, workspaceId)
        )
      );

    return createSuccessResponse();
  } catch (error) {
    console.error('Delete chat error:', error);
    return createErrorResponse('Failed to delete chat');
  }
}

export const deleteAllUserChatInServer = async (workspaceId: string) => {
  // 验证用户权限和 workspace 访问
  const validation = await getSessionAndValidateWorkspace(workspaceId);
  if (!validation.hasAccess) {
    return createErrorResponse(validation.error || 'Access denied');
  }

  try {
    // 删除当前用户在指定 workspace 中的所有聊天记录
    await db.delete(chats)
      .where(
        and(
          eq(chats.userId, validation.userId!),
          eq(chats.workspaceId, workspaceId)
        )
      );

    // 删除当前用户在指定 workspace 中的所有消息记录
    await db.delete(messages)
      .where(
        and(
          eq(messages.userId, validation.userId!),
          eq(messages.workspaceId, workspaceId)
        )
      );

    return createSuccessResponse();
  } catch (error) {
    console.error('Delete all user chats error:', error);
    return createErrorResponse('Failed to delete all chats');
  }
}

export const fetchAppSettings = async (key: string, workspaceId: string) => {
  const result = await db.query.appSettings
    .findFirst({
      where: and(
        eq(appSettings.key, key),
        eq(appSettings.workspaceId, workspaceId)
      )
    });
  return result?.value;
}

export const fetchLlmSettings = async (providerId: string, workspaceId: string) => {
  const result = await db.query.llmSettingsTable
    .findFirst({
      where: and(
        eq(llmSettingsTable.provider, providerId),
        eq(llmSettingsTable.workspaceId, workspaceId)
      )
    });
  return result;
}

export const fetchSettingsByKeys = async (keys: Array<string>, workspaceId: string) => {
  const results = await db.query.appSettings
    .findMany({
      where: and(
        inArray(appSettings.key, keys),
        eq(appSettings.workspaceId, workspaceId)
      )
    });

  // Initialize the result object with all requested keys set to null
  const settingsObject = keys.reduce((acc, key) => {
    acc[key] = null;
    return acc;
  }, {} as Record<string, string | null>);

  // Update the values for keys that exist in the database
  results.forEach(setting => {
    settingsObject[setting.key] = setting.value;
  });

  return settingsObject;
}

export const getMcpServersAndAvailableTools = async (workspaceId: string) => {
  // 验证用户权限和 workspace 访问
  const validation = await getSessionAndValidateWorkspace(workspaceId);
  if (!validation.hasAccess) {
    return {
      tools: [],
      mcpServers: []
    };
  }

  try {
    const tools = await db
      .select({
        name: mcpTools.name,
        description: mcpTools.description,
        serverId: mcpTools.serverId,
        inputSchema: mcpTools.inputSchema,
      })
      .from(mcpTools)
      .leftJoin(mcpServers, eq(mcpTools.serverId, mcpServers.id))
      .orderBy(
        asc(mcpTools.serverId),
      )
      .where(
        and(
          eq(mcpServers.isActive, true),
          eq(mcpServers.workspaceId, workspaceId)
        )
      );

    const servers = await db.query.mcpServers.findMany({
      where: and(
        eq(mcpServers.isActive, true),
        eq(mcpServers.workspaceId, workspaceId)
      ),
      orderBy: [mcpServers.createdAt],
    });

    return {
      tools,
      mcpServers: servers
    };
  } catch (error) {
    console.error('Get MCP servers and tools error:', error);
    return {
      tools: [],
      mcpServers: []
    };
  }
}

export const syncMcpTools = async (messageId: number, mcpToolsResponse: MCPToolResponse[]) => {
  try {
    await db.update(messages)
      .set({
        mcpTools: mcpToolsResponse,
        updatedAt: new Date()
      })
      .where(eq(messages.id, messageId));

    return {
      status: 'success',
      message: '工具信息已保存'
    };
  } catch (error) {
    console.error('同步 MCP 工具响应失败:', error);
    return {
      status: 'fail',
      message: '同步工具失败'
    };
  }
}

export const getSearchResult = async (workspaceId: string, keyword: string): Promise<{
  status: string;
  message: string;
  data: WebSearchResponse | null;
}> => {
  // 验证用户权限和 workspace 访问
  const validation = await getSessionAndValidateWorkspace(workspaceId);
  if (!validation.hasAccess) {
    return {
      status: 'error',
      message: validation.error || 'Access denied',
      data: null
    };
  }

  try {
    const searchConfig = await db.query.searchEngineConfig.findFirst({
      where: and(
        eq(searchEngineConfig.isActive, true),
        eq(searchEngineConfig.workspaceId, workspaceId)
      )
    });

    if (searchConfig) {
      try {
        const webSearch = await WebSearchService.search({
          id: searchConfig.id,
          name: searchConfig.name,
          apiKey: searchConfig.apiKey as string
        }, keyword, searchConfig.maxResults);

        return {
          status: 'success',
          message: 'success',
          data: webSearch
        };
      } catch (error) {
        return {
          status: 'error',
          message: error instanceof Error ? error.message : 'Unknown error',
          data: null,
        };
      }
    } else {
      return {
        status: 'error',
        message: '管理员未配置搜索',
        data: null
      };
    }
  } catch (error) {
    console.error('Get search result error:', error);
    return {
      status: 'error',
      message: 'Failed to get search result',
      data: null
    };
  }
}
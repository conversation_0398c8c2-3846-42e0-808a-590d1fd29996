'use client';
import App from "@/app/components/App";
import React, { useEffect } from 'react';
import { redirect } from 'next/navigation';
import { useSession } from 'next-auth/react';
import ChatPrepare from "@/app/components/ChatPrepare";
import { App as AntdApp } from 'antd';

export default function ChatLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { workspace: string };
}) {
  const { status } = useSession();
  useEffect(() => {
    if (status === 'unauthenticated') {
      redirect('/login');
    }
  }, [status]);
  return (
    <div className="flex flex-col h-dvh">
      <ChatPrepare />
      <AntdApp>
        <App>{children}</App>
      </AntdApp>
    </div>
  )
}
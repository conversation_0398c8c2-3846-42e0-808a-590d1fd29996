'use client'
import React from 'react'
import { <PERSON><PERSON>, message, <PERSON><PERSON>, Popconfirm } from "antd";
import useChatListStore from '@/app/store/chatList';
import { deleteAllUserChatInServer } from '@/app/[workspace]/chat/actions/chat';
import { useTranslations } from 'next-intl';
import { useParams, useRouter } from 'next/navigation';
import { getWorkspaceInfo } from '@/app/actions/workspace';
import { useSession } from 'next-auth/react';
import { leaveWorkspace } from './actions';

const System = () => {
  const c = useTranslations('Common');
  const t = useTranslations('Settings');
  const params = useParams();
  const { data: session } = useSession();
  const workspaceId = params.workspace as string;
  const [modal, contextHolderModal] = Modal.useModal();
  const { setChatList } = useChatListStore();

  // 获取工作空间信息和用户权限
  const [workspaceInfo, setWorkspaceInfo] = React.useState<any>(null);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    const fetchWorkspaceInfo = async () => {
      try {
        const result = await getWorkspaceInfo(workspaceId);
        if (result.status === 'success') {
          setWorkspaceInfo(result.data);
        }
      } catch (error) {
        console.error('Failed to fetch workspace info:', error);
      } finally {
        setLoading(false);
      }
    };

    if (workspaceId && session?.user?.id) {
      fetchWorkspaceInfo();
    }
  }, [workspaceId, session?.user?.id]);

  const clearAllChats = () => {
    modal.confirm({
      title: t('deleteAllMessagesTitle'),
      content: t('deleteAllMessagesDesc'),
      okText: c('confirm'),
      cancelText: c('cancel'),
      onOk() {
        deleteAllUserChatInServer(workspaceId);
        setChatList([]);
        message.success(t('deleteSuccess'));
      }
    });
  }

  const handleLeaveWorkspace = async () => {
    try {
      const result = await leaveWorkspace(workspaceId);
      if (result.status === 'success') {
        message.success('成功退出工作空间');
        // 跳转到首页或用户的其他工作空间
        window.location.href = '/workspaces';
      } else {
        message.error(result.message || '退出工作空间失败');
      }
    } catch (error) {
      console.error('Leave workspace error:', error);
      message.error('退出工作空间时发生错误');
    }
  };

  // 判断当前用户是否为workspace的owner
  const isOwner = workspaceInfo?.role === 'owner';

  return (
    <div>
      {contextHolderModal}
      <div className='flex flex-row justify-between mt-6 p-6 border border-gray-200 rounded-md'>
        <div className='flex flex-col '>
          <span className='text-sm'>{t('deleteAllMessagesTitle')}</span>
          <span className='text-gray-400 text-xs'>{t('deleteAllMessagesDesc')}</span>
        </div>
        <div className='flex items-center'>
          <Button onClick={clearAllChats}>{t('deleteAction')}</Button>
        </div>
      </div>

      {/* 退出工作空间区域 - 只有非owner用户才能看到 */}
      {!loading && !isOwner && (
        <div className='flex flex-row justify-between mt-6 p-6 border border-gray-200 rounded-md'>
          <div className='flex flex-col '>
            <span className='text-sm'>退出此工作空间</span>
            <span className='text-gray-400 text-xs'>退出后将无法在此空间使用 AI 聊天等功能</span>
          </div>
          <div className='flex items-center'>
            <Popconfirm
              title="确认要退出吗？"
              description="退出后将无法在此空间继续使用 AI 聊天等功能"
              onConfirm={handleLeaveWorkspace}
              okText={c('confirm')}
              cancelText={c('cancel')}
            >
              <Button>退出</Button>
            </Popconfirm>
          </div>
        </div>
      )}

    </div>
  )
}

export default System
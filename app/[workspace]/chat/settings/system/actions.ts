'use server';
import { db } from '@/app/db';
import { auth } from "@/auth";
import { eq, and } from 'drizzle-orm';
import { userWorkspace } from '@/app/db/schema';

/**
 * 退出工作空间
 * @param workspaceId - 工作空间ID
 * @returns Promise<{status: string, message: string}>
 */
export async function leaveWorkspace(workspaceId: string) {
  try {
    // 获取当前用户session
    const session = await auth();
    if (!session?.user?.id) {
      return {
        status: 'fail',
        message: 'User not authenticated'
      };
    }

    const userId = session.user.id;

    // 检查用户是否在工作空间中
    const userWorkspaceRecord = await db.query.userWorkspace.findFirst({
      where: and(
        eq(userWorkspace.userId, userId),
        eq(userWorkspace.workspaceId, workspaceId),
        eq(userWorkspace.isActive, true)
      ),
      with: {
        workspace: true
      }
    });

    if (!userWorkspaceRecord) {
      return {
        status: 'fail',
        message: 'You are not a member of this workspace'
      };
    }

    // 检查用户是否为workspace的owner
    if (userWorkspaceRecord.role === 'owner') {
      return {
        status: 'fail',
        message: 'Workspace owner cannot leave the workspace'
      };
    }

    // 将用户从工作空间中移除（软删除）
    await db.update(userWorkspace).set({
      isActive: false,
      updatedAt: new Date()
    }).where(and(
      eq(userWorkspace.userId, userId),
      eq(userWorkspace.workspaceId, workspaceId)
    ));

    return {
      status: 'success',
      message: 'Successfully left the workspace'
    };

  } catch (error) {
    console.error('Leave workspace error:', error);
    return {
      status: 'fail',
      message: 'Failed to leave workspace'
    };
  }
}

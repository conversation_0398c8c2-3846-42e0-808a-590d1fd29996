'use client';
import Image from 'next/image';
import Link from 'next/link';
import React, { useState, useEffect } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { BotType } from '@/app/db/schema';
import { getBotListInServer } from '@/app/[workspace]/chat/actions/bot';
import { Button, Skeleton, Empty } from 'antd';
import { useSession } from 'next-auth/react';
import { useTranslations } from 'next-intl';
import { useParams, redirect } from 'next/navigation';

const BotDiscover = () => {
  const t = useTranslations('Chat');
  const params = useParams();
  const workspaceId = params.workspace as string;
  const { status } = useSession();
  const [botList, setBotList] = useState<BotType[]>([]);
  const [isPending, setIsPending] = useState(true);

  useEffect(() => {
    if (status === 'unauthenticated') {
      redirect('/login');
    }
  }, [status]);

  useEffect(() => {
    async function getBotsList() {
      if (workspaceId) {
        const bots = await getBotListInServer(workspaceId)
        setBotList(bots.data as BotType[]);
        setIsPending(false);
      }
    }
    getBotsList();
  }, [workspaceId]);

  return (
    <div className="container max-w-4xl mx-auto p-4">
      <div className='w-full flex flex-row justify-between items-center'>
        <h1 className='text-xl font-bold mb-4 mt-4'>{t('discoverBots')}</h1>
        <Link href={`/${workspaceId}/chat/bot/create`}>
          <Button type="primary" icon={<PlusOutlined />} shape='round'>
            <div className='flex flex-row'>
              {t('createBot')}
            </div>
          </Button>
        </Link>
      </div>

      {isPending ?
        <div className="grid grid-cols-2 gap-4">
          <SkeletonCard />
          <SkeletonCard />
          <SkeletonCard />
          <SkeletonCard />
        </div>
        :
        botList.length === 0 ? (
          <div className="text-center py-16">
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <span>
                  暂无可用的智能体
                  <br />
                  创建第一个智能体开始使用
                </span>
              }
            >
              <Link href={`/${workspaceId}/chat/bot/create`}>
                <Button shape='round' type="primary" icon={<PlusOutlined />}>
                  创建智能体
                </Button>
              </Link>
            </Empty>
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-4">
            {botList.map((item, index) => (
              <ServiceCard key={index} bot={item} workspaceId={workspaceId} />
            ))}
          </div>
        )
      }
    </div>
  )
};

const SkeletonCard = () => {
  return (
    <div className="bg-white rounded-xl border-gray-200 border p-4 shadow-sm hover:shadow-md transition-shadow duration-200 w-full">
      <div className="flex items-start gap-4">
        <Skeleton.Avatar active size={48} style={{ borderRadius: 8 }} shape='square' />
        <div className="flex flex-col w-full">
          <Skeleton.Node active style={{ width: '100%', height: 22 }} />
          <Skeleton.Node active style={{ width: '90%', height: 16, marginTop: 8 }} />
        </div>
      </div>
    </div>
  )
}
const ServiceCard = (props: { bot: BotType; workspaceId: string }) => {
  return (
    <div className="bg-white rounded-xl border-gray-200 border p-4 shadow-sm hover:shadow-md transition-shadow duration-200 w-full relative">
      {props.bot.creator === 'public' && (
        <span className="absolute top-2 right-2 bg-gray-100 text-gray-500 text-xs  px-2 py-0.5 rounded">
          公共
        </span>
      )}
      <Link href={`/${props.workspaceId}/chat/bot/${props.bot.id}`}>
        <div className="flex items-start gap-4">
          <div className="w-12 h-12 rounded-lg flex bg-slate-200 items-center justify-center  overflow-hidden flex-shrink-0">
            {props.bot.avatarType === 'emoji' && <span className="text-4xl">{props.bot.avatar}</span>}
            {props.bot.avatarType === 'url' &&
              <Image
                src={props.bot.avatar}
                alt={props.bot.title}
                width={52}
                height={52}
                className="w-full h-full object-cover"
                unoptimized
              />}
          </div>

          <div className="flex-1 min-w-0">
            <h3 className="text-gray-900 font-medium text-lg mb-1 truncate">
              {props.bot.title}
            </h3>
            <p className="text-gray-500 text-sm line-clamp-2">
              {props.bot.desc}
            </p>
          </div>
        </div>
      </Link>
    </div>
  );
};

export default BotDiscover;

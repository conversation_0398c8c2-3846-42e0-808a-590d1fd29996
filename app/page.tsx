import { redirect } from 'next/navigation';
import { auth } from '@/auth';
import { getUserFirstWorkspace } from '@/app/actions/workspace';

export default async function Page() {
  const session = await auth();

  // 如果用户未登录，跳转到登录页面
  if (!session?.user) {
    redirect('/login');
  }

  // 获取用户的第一个工作区
  const firstWorkspaceResult = await getUserFirstWorkspace();

  if (firstWorkspaceResult.status === 'success' && firstWorkspaceResult.data) {
    // 用户有工作区，跳转到第一个工作区的聊天页面
    redirect(`/${firstWorkspaceResult.data.workspaceId}/chat`);
  } else if (firstWorkspaceResult.status === 'fail') {
    // 数据库查询失败，回退到工作区选择页面
    redirect('/workspaces');
  } else {
    redirect('/onboarding');
  }
}
// 可复用的样式常量
export const CONTROL_BUTTON_STYLES = {
  base: 'ml-2 flex h-7 flex-row items-center pr-3 pl-2 py-1 cursor-pointer rounded-[10px] border hover:bg-gray-100 text-gray-500',
  active: 'ml-2 flex h-7 flex-row items-center pr-3 pl-2 py-1 cursor-pointer rounded-[10px] border bg-blue-100 text-blue-600 border-blue-400',
  disabled: 'ml-2 flex h-7 flex-row items-center pr-3 pl-2 py-1 cursor-not-allowed rounded-[10px] border opacity-50 text-gray-400',
} as const;

// 更灵活的变体函数
export const getControlButtonClass = (variant: 'base' | 'active' | 'disabled' = 'base') => {
  return CONTROL_BUTTON_STYLES[variant];
};

// 如果需要更复杂的变体组合
export const createControlButtonClass = (options: {
  active?: boolean;
  disabled?: boolean;
  spacing?: 'tight' | 'normal' | 'loose';
}) => {
  const { active = false, disabled = false, spacing = 'normal' } = options;

  const spacingClasses = {
    tight: 'ml-1',
    normal: 'ml-2',
    loose: 'ml-4',
  };

  const baseClasses = 'flex h-7 flex-row items-center pr-3 pl-2 py-1 cursor-pointer rounded-[10px] border';

  let classes = `${spacingClasses[spacing]} ${baseClasses}`;

  if (disabled) {
    classes += ' opacity-50 cursor-not-allowed text-gray-400';
  } else if (active) {
    classes += ' bg-blue-100 text-blue-600 border-blue-400';
  } else {
    classes += ' hover:bg-gray-100 text-gray-500';
  }

  return classes;
};

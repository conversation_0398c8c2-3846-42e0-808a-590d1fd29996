import { SearchContentItem } from "@/types/llm";

export const fetchFavicon = async (url: string): Promise<string | null> => {
  try {
    const urlObj = new URL(url);
    const faviconUrl = `${urlObj.protocol}//${urlObj.host}/favicon.ico`;

    const response = await fetch(faviconUrl, {
      method: 'HEAD',
      mode: 'no-cors'
    });

    if (response.ok || response.type === 'opaque') {
      return faviconUrl;
    }

    return null;
  } catch (error) {
    console.error('Failed to fetch favicon:', error);
    return null;
  }
}

export const replaceMarkdownLinkTextWithDomain = (text: string, annotations?: SearchContentItem[]): string => {
  const markdownLinkRegex = /\(\[(.*?)\]\((https?:\/\/[^\s)]+)\)\)/g;
  if (!annotations?.length) return text
  return text.replaceAll(markdownLinkRegex, (_, linkText, url) => {
    const annotationIndex = annotations?.findIndex(ann => ann.data.url === url) as number
    const annotation = annotations?.[annotationIndex]
    if (annotation) {
      return `<sup data-annotation-title="${annotation.data.title}" data-annotation-favicon="${annotation.data.metadata?.favicon}" data-annotation-url="${url}">${annotationIndex + 1}</sup>`;
    }
    return _
  });
};

'use server';
import { MCPTool, MCPServer } from '@/types/llm';
import mcpService from '@/app/services/MCPService';
import { mcpServers } from '@/app/db/schema';
import { db } from '@/app/db';
import { eq } from 'drizzle-orm';
import { MCPError, MCPErrorFactory, MCPErrorHandler } from '@/types/errorTypes';

export async function callMCPTool(tool: MCPTool): Promise<any> {
  try {
    // 查询服务器信息
    const server = await db.query.mcpServers.findFirst({
      where: eq(mcpServers.name, tool.serverName),
    });

    if (!server) {
      throw MCPErrorFactory.serverNotFound(tool.serverName);
    }

    // 检查服务器是否激活
    if (!server.isActive) {
      throw MCPErrorFactory.serverInactive(tool.serverName);
    }

    // 构建服务器信息
    const serverInfo: MCPServer = {
      name: server.name,
      description: server.description || undefined,
      type: server.type || 'sse',
      baseUrl: server.baseUrl,
      isActive: true,
    };

    // 调用MCP工具
    const toolInfo = {
      name: tool.name,
      args: tool.inputSchema,
    };

    const result = await mcpService.callTool({
      server: serverInfo,
      ...toolInfo
    });

    return result;

  } catch (error) {
    // 统一错误处理
    let mcpError: MCPError;

    if (MCPErrorHandler.isMCPError(error)) {
      mcpError = error;
    } else {
      mcpError = MCPErrorHandler.fromError(error as Error, {
        serverName: tool.serverName,
        toolName: tool.name,
        args: tool.inputSchema
      });
    }

    // 记录错误日志
    MCPErrorHandler.logError(mcpError);

    // 对于大部分错误类型，我们返回错误响应而不是抛出异常
    // 这样可以保持与现有UI的兼容性，让用户看到友好的错误消息
    if (mcpError.code === 'SERVER_NOT_FOUND' ||
        mcpError.code === 'TOOL_NOT_FOUND' ||
        mcpError.code === 'SERVER_INACTIVE' ||
        mcpError.code === 'UNKNOWN_ERROR' ||
        mcpError.code === 'TOOL_CALL_FAILED' ||
        mcpError.code === 'TOOL_INVALID_ARGS') {
      return {
        isError: true,
        content: [
          {
            type: 'text',
            text: mcpError.toUserMessage()
          }
        ]
      };
    }

    // 只对严重的系统级错误（如连接超时）抛出异常让上层处理
    throw mcpError;
  }
}

interface ErrorResponseBody {
  response: {
    code: string;
    data: any;
    message: string;
  };
}

export function to<T, U = Error & ErrorResponseBody>(
  promise: Promise<T>,
  errorExt?: object
): Promise<[U, undefined] | [null, T]> {
  return promise
    .then<[null, T]>((data: T) => [null, data])
    .catch<[U, undefined]>((err: U) => {
      if (errorExt) {
        const parsedError = { ...err, ...errorExt };
        return [parsedError, undefined];
      }

      return [err, undefined];
    });
}

export default to;

/**
 * 验证 callbackUrl 的安全性
 * 确保只允许跳转到本域名下的地址，防止开放重定向攻击
 */
export function validateCallbackUrl(callbackUrl: string | null): string | null {
  if (!callbackUrl) {
    return null;
  }

  try {
    // URL 解码
    const decodedUrl = decodeURIComponent(callbackUrl);
    
    // 创建 URL 对象进行解析
    const url = new URL(decodedUrl);
    
    // 获取当前域名配置
    const nextAuthUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const allowedOrigin = new URL(nextAuthUrl);
    
    // 验证协议和域名
    if (url.protocol !== allowedOrigin.protocol || url.hostname !== allowedOrigin.hostname) {
      console.warn(`Invalid callbackUrl: ${decodedUrl} - protocol or hostname mismatch`);
      return null;
    }
    
    // 验证端口（如果配置了端口）
    if (allowedOrigin.port && url.port !== allowedOrigin.port) {
      console.warn(`Invalid callbackUrl: ${decodedUrl} - port mismatch`);
      return null;
    }
    
    // 返回验证通过的 URL
    return decodedUrl;
  } catch (error) {
    console.error('Error validating callbackUrl:', error);
    return null;
  }
}

/**
 * 从查询参数中提取并验证 callbackUrl
 */
export function extractAndValidateCallbackUrl(searchParams: URLSearchParams): string {
  const callbackUrl = searchParams.get('callbackUrl');
  const validatedUrl = validateCallbackUrl(callbackUrl);
  
  // 如果验证失败，返回默认跳转地址
  return validatedUrl || '/';
}

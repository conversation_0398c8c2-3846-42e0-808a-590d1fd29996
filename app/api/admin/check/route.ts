import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/app/db';
import { users } from '@/app/db/schema';
import { eq } from 'drizzle-orm';

export async function GET() {
  try {
    // 获取当前用户会话
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ isAdmin: false }, { status: 401 });
    }

    // 查询用户的管理员状态
    const user = await db
      .select({
        isAdmin: users.isAdmin
      })
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    if (!user.length || !user[0].isAdmin) {
      return NextResponse.json({ isAdmin: false }, { status: 403 });
    }

    return NextResponse.json({ isAdmin: true });
  } catch (error) {
    console.error('Global admin check error:', error);
    return NextResponse.json({ isAdmin: false }, { status: 500 });
  }
}
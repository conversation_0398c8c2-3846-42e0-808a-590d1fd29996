import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { db } from '@/app/db'
import { webhookEvents } from '@/app/db/schema'
import { desc, eq, and } from 'drizzle-orm'

export async function GET(
  request: NextRequest,
  { params }: { params: { workspace: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const workspaceId = params.workspace;
    
    // 检查用户是否有权限访问该工作空间（这里简化处理，实际应该检查用户权限）
    // 可以根据需要添加更严格的权限检查

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '20');
    const status = searchParams.get('status');
    const eventType = searchParams.get('eventType');

    // 构建查询条件
    const conditions = [];
    if (status) {
      conditions.push(eq(webhookEvents.status, status as any));
    }
    if (eventType) {
      conditions.push(eq(webhookEvents.eventType, eventType));
    }

    // 查询 webhook 事件
    const events = await db.query.webhookEvents.findMany({
      where: conditions.length > 0 ? and(...conditions) : undefined,
      orderBy: [desc(webhookEvents.createdAt)],
      limit: pageSize,
      offset: (page - 1) * pageSize,
    });

    // 获取总数（简化处理，实际可能需要单独查询）
    const totalEvents = await db.query.webhookEvents.findMany({
      where: conditions.length > 0 ? and(...conditions) : undefined,
    });

    return NextResponse.json({
      success: true,
      events,
      pagination: {
        page,
        pageSize,
        total: totalEvents.length,
        totalPages: Math.ceil(totalEvents.length / pageSize),
      }
    });

  } catch (error) {
    console.error('Failed to fetch webhook events:', error);
    return NextResponse.json(
      { error: 'Failed to fetch webhook events' },
      { status: 500 }
    );
  }
}

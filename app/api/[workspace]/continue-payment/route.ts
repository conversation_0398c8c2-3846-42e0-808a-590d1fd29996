import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { stripe } from '@/lib/stripe'
import { auth } from '@/auth'
import { db } from '@/app/db'
import { purchaseOrders } from '@/app/db/schema'
import { eq, and } from 'drizzle-orm'

// 产品配置映射
const PRODUCT_CONFIG = {
  'product_3': {
    priceId: 'price_1RsacHFY4c6t1XyODkTZr2E2',
    name: '1,500,000 积分',
    amount: 300,
    credits: 1500000,
  },
  'product_10': {
    priceId: 'price_1RsbGgFY4c6t1XyOwgnsCReC',
    name: '6,000,000 积分',
    amount: 1000,
    credits: 6000000,
  },
  'product_100': {
    priceId: 'price_1RsbH4FY4c6t1XyOVdJy4uOP',
    name: '80,000,000 积分',
    amount: 10000,
    credits: 80000000,
  },
} as const;

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ workspace: string }> }
) {
  try {
    // 验证用户身份
    const session = await auth();
    if (!session?.user?.id || !session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 获取 workspaceId
    const { workspace: workspaceId } = await params;

    if (!workspaceId) {
      return NextResponse.json(
        { error: 'Workspace ID is required' },
        { status: 400 }
      );
    }

    // 获取请求体
    const body = await req.json();
    const { orderId, productId } = body;

    // 验证必需参数
    if (!orderId && !productId) {
      return NextResponse.json(
        { error: 'Either orderId or productId is required' },
        { status: 400 }
      );
    }

    let existingOrder;

    if (orderId) {
      // 通过订单ID查找现有订单
      existingOrder = await db.query.purchaseOrders.findFirst({
        where: and(
          eq(purchaseOrders.id, orderId),
          eq(purchaseOrders.userId, session.user.id!),
          eq(purchaseOrders.workspaceId, workspaceId),
          eq(purchaseOrders.status, 'pending')
        )
      });

      if (!existingOrder) {
        return NextResponse.json(
          { error: 'Pending order not found or not accessible' },
          { status: 404 }
        );
      }
    } else if (productId) {
      // 通过产品ID查找最新的pending订单
      if (!(productId in PRODUCT_CONFIG)) {
        return NextResponse.json(
          { error: 'Invalid product ID' },
          { status: 400 }
        );
      }

      existingOrder = await db.query.purchaseOrders.findFirst({
        where: and(
          eq(purchaseOrders.userId, session.user.id!),
          eq(purchaseOrders.workspaceId, workspaceId),
          eq(purchaseOrders.status, 'pending'),
          eq(purchaseOrders.packageType, productId as 'product_3' | 'product_10' | 'product_100')
        ),
        orderBy: (purchaseOrders, { desc }) => [desc(purchaseOrders.createdAt)]
      });

      if (!existingOrder) {
        return NextResponse.json(
          { error: 'No pending order found for this product' },
          { status: 404 }
        );
      }
    }

    // 检查订单是否过期（30分钟）
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
    if (existingOrder!.createdAt && existingOrder!.createdAt < thirtyMinutesAgo) {
      return NextResponse.json(
        { error: 'Order has expired. Please create a new order.' },
        { status: 410 }
      );
    }

    const product = PRODUCT_CONFIG[existingOrder!.packageType];
    const headersList = headers();
    const origin = headersList.get('origin');

    if (!origin) {
      return NextResponse.json(
        { error: 'Origin header is required' },
        { status: 400 }
      );
    }

    // 尝试检索现有的 Stripe 会话
    let stripeSession;
    let shouldCreateNewSession = false;

    if (existingOrder!.stripeSessionId) {
      try {
        stripeSession = await stripe.checkout.sessions.retrieve(existingOrder!.stripeSessionId);
        
        // 检查会话状态，如果已过期或已完成，需要创建新会话
        if (stripeSession.status === 'expired' || stripeSession.status === 'complete') {
          shouldCreateNewSession = true;
        } else if (stripeSession.url) {
          // 会话仍然有效，直接返回现有URL
          return NextResponse.json({ 
            url: stripeSession.url,
            orderId: existingOrder!.id,
            isExistingSession: true
          });
        }
      } catch (error) {
        console.error('Failed to retrieve existing Stripe session:', error);
        shouldCreateNewSession = true;
      }
    } else {
      shouldCreateNewSession = true;
    }

    // 创建新的 Stripe Checkout Session
    if (shouldCreateNewSession) {
      const newCheckoutSession = await stripe.checkout.sessions.create({
        line_items: [
          {
            price: product.priceId,
            quantity: 1,
          },
        ],
        mode: 'payment',
        success_url: `${origin}/${workspaceId}/admin/plans/success?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${origin}/${workspaceId}/admin/plans?canceled=true`,
        metadata: {
          workspaceId,
          userId: session.user.id,
          productId: existingOrder!.packageType,
          orderId: existingOrder!.id, // 添加订单ID到元数据
        },
        customer_email: session.user.email || undefined,
      });

      if (!newCheckoutSession.url) {
        return NextResponse.json(
          { error: 'Failed to create checkout session URL' },
          { status: 500 }
        );
      }

      // 更新现有订单的 stripeSessionId
      try {
        await db.update(purchaseOrders)
          .set({
            stripeSessionId: newCheckoutSession.id
          })
          .where(eq(purchaseOrders.id, existingOrder!.id));
      } catch (error) {
        console.error('Failed to update order with new session ID:', error);
        // 即使更新失败，也返回支付链接
      }

      return NextResponse.json({ 
        url: newCheckoutSession.url,
        orderId: existingOrder!.id,
        isExistingSession: false
      });
    }

    // 如果到这里，说明有问题
    return NextResponse.json(
      { error: 'Unable to create or retrieve payment session' },
      { status: 500 }
    );

  } catch (err) {
    console.error('Continue payment error:', err);
    const error = err as Error & { statusCode?: number };
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: error.statusCode || 500 }
    );
  }
}

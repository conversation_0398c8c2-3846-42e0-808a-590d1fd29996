import { LLMModel } from '@/types/llm';
import { modelList } from '@/app/db/data/models/hivechat';


/**
 * 积分计算结果接口
 */
export interface CreditCalculation {
  inputCredits: number;
  outputCredits: number;
  totalCredits: number;
}

/**
 * 模型未找到错误类
 */
export class ModelNotFoundError extends Error {
  constructor(modelId: string) {
    super(`Model '${modelId}' not found in HiveChat model list`);
    this.name = 'ModelNotFoundError';
  }
}

/**
 * 根据模型ID获取价格系数
 * @param modelId 模型ID
 * @returns 价格系数配置
 * @throws ModelNotFoundError 当模型不存在时抛出异常
 */
export function getPriceCoefficient(modelId: string): {
  inputCoefficient: number;
  outputCoefficient: number;
} {
  const model = modelList.find((m: LLMModel) => m.id === modelId);

  if (!model) {
    throw new ModelNotFoundError(modelId);
  }

  return {
    inputCoefficient: model.inputPriceCoefficient ?? 1.5,
    outputCoefficient: model.outputPriceCoefficient ?? 1.5
  };
}

/**
 * 计算指定token数量对应的积分扣除额度
 * @param modelId 模型ID
 * @param inputTokens 输入token数量
 * @param outputTokens 输出token数量
 * @returns 积分计算结果（包含四舍五入处理）
 * @throws ModelNotFoundError 当模型不存在时抛出异常
 */
export function calculateCredits(
  modelId: string,
  inputTokens: number,
  outputTokens: number
): CreditCalculation {
  const { inputCoefficient, outputCoefficient } = getPriceCoefficient(modelId);

  // 计算积分扣除（四舍五入处理）
  const inputCredits = Math.round(inputTokens * inputCoefficient);
  const outputCredits = Math.round(outputTokens * outputCoefficient);
  const totalCredits = inputCredits + outputCredits;

  return {
    inputCredits,
    outputCredits,
    totalCredits
  };
}
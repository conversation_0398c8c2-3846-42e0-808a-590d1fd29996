import { NextRequest, NextResponse } from 'next/server'
import { stripe } from '@/lib/stripe'
import { auth } from '@/auth'
import { db } from '@/app/db'
import { purchaseOrders, userWorkspace, creditTransactions } from '@/app/db/schema'
import { eq, and, sql } from 'drizzle-orm'

// 辅助函数：处理订单完成逻辑
async function processOrderCompletion(order: any, workspaceId: string, userId: string) {
  try {
    await db.transaction(async (tx) => {
      // 1. 更新订单状态
      await tx.update(purchaseOrders)
        .set({
          status: 'completed',
          completedAt: new Date()
        })
        .where(eq(purchaseOrders.id, order.id));

      // 2. 更新用户积分余额
      await tx.update(userWorkspace)
        .set({
          creditBalance: sql`${userWorkspace.creditBalance} + ${order.creditsPurchased}`
        })
        .where(and(
          eq(userWorkspace.userId, userId),
          eq(userWorkspace.workspaceId, workspaceId)
        ));

      // 3. 获取更新后的用户积分余额
      const updatedUserWorkspace = await tx.query.userWorkspace.findFirst({
        where: and(
          eq(userWorkspace.userId, userId),
          eq(userWorkspace.workspaceId, workspaceId)
        )
      });

      if (!updatedUserWorkspace) {
        throw new Error('User workspace not found');
      }

      // 4. 创建积分交易记录
      await tx.insert(creditTransactions).values({
        workspaceId,
        userId,
        transactionType: 'purchase',
        inputCredits: 0,
        outputCredits: 0,
        totalCredits: order.creditsPurchased,
        balanceAfter: updatedUserWorkspace.creditBalance,
        relatedEntityId: order.id,
        relatedEntityType: 'PurchaseOrder',
        notes: `Purchase completed: ${order.packageType}, Amount: $${order.amountPaidCents / 100}`
      });
    });

    return NextResponse.json({
      success: true,
      status: 'completed',
      message: '支付成功，积分已到账',
      order: {
        id: order.id,
        creditsPurchased: order.creditsPurchased,
        amountPaid: order.amountPaidCents / 100
      }
    });

  } catch (error) {
    console.error('Failed to complete order:', error);
    return NextResponse.json(
      { error: 'Failed to complete order processing' },
      { status: 500 }
    );
  }
}

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ workspace: string }> }
) {
  try {
    // 验证用户身份
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 获取 workspaceId
    const { workspace: workspaceId } = await params;

    if (!workspaceId) {
      return NextResponse.json(
        { error: 'Workspace ID is required' },
        { status: 400 }
      );
    }

    // 获取请求体
    const body = await req.json();
    const { sessionId } = body;

    if (!sessionId || typeof sessionId !== 'string') {
      return NextResponse.json(
        { error: 'Valid session ID is required' },
        { status: 400 }
      );
    }

    // 验证sessionId格式（Stripe session ID通常以cs_开头）
    if (!sessionId.startsWith('cs_')) {
      return NextResponse.json(
        { error: 'Invalid session ID format' },
        { status: 400 }
      );
    }

    // 从 Stripe 获取支付会话信息
    let stripeSession;
    try {
      stripeSession = await stripe.checkout.sessions.retrieve(sessionId);
    } catch (error) {
      console.error('Failed to retrieve Stripe session:', error);
      return NextResponse.json(
        { error: 'Invalid session ID' },
        { status: 400 }
      );
    }

    // 验证支付状态
    if (stripeSession.payment_status !== 'paid') {
      return NextResponse.json({
        success: false,
        status: stripeSession.payment_status,
        message: '支付尚未完成'
      });
    }

    // 验证会话元数据
    if (stripeSession.metadata?.workspaceId !== workspaceId || 
        stripeSession.metadata?.userId !== session.user.id) {
      return NextResponse.json(
        { error: 'Session metadata mismatch' },
        { status: 403 }
      );
    }

    // 查找对应的订单记录
    const existingOrder = await db.query.purchaseOrders.findFirst({
      where: eq(purchaseOrders.stripeSessionId, sessionId)
    });

    if (!existingOrder) {
      // 如果订单记录不存在，可能是创建订单时失败了，尝试创建订单记录
      const productId = stripeSession.metadata?.productId;
      if (!productId) {
        return NextResponse.json(
          { error: 'Order not found and cannot create from session metadata' },
          { status: 404 }
        );
      }

      // 从产品配置获取积分信息
      const PRODUCT_CONFIG = {
        'product_3': { credits: 1500000, amount: 300 },
        'product_10': { credits: 6000000, amount: 1000 },
        'product_100': { credits: 80000000, amount: 10000 },
      } as const;

      const product = PRODUCT_CONFIG[productId as keyof typeof PRODUCT_CONFIG];
      if (!product) {
        return NextResponse.json(
          { error: 'Invalid product configuration' },
          { status: 400 }
        );
      }

      // 创建缺失的订单记录
      try {
        await db.insert(purchaseOrders).values({
          workspaceId,
          userId: session.user.id!,
          packageType: productId as 'product_3' | 'product_10' | 'product_100',
          stripeSessionId: sessionId,
          status: 'pending',
          amountPaidCents: product.amount,
          currency: 'usd',
          creditsPurchased: product.credits,
        });

        // 重新查询订单记录
        const newOrder = await db.query.purchaseOrders.findFirst({
          where: eq(purchaseOrders.stripeSessionId, sessionId)
        });

        if (!newOrder) {
          return NextResponse.json(
            { error: 'Failed to create order record' },
            { status: 500 }
          );
        }

        // 继续处理新创建的订单
        return await processOrderCompletion(newOrder, workspaceId, session.user.id!);
      } catch (error) {
        console.error('Failed to create missing order record:', error);
        return NextResponse.json(
          { error: 'Failed to create order record' },
          { status: 500 }
        );
      }
    }

    // 如果订单已经完成，直接返回成功
    if (existingOrder.status === 'completed') {
      return NextResponse.json({
        success: true,
        status: 'completed',
        message: '支付已完成',
        order: {
          id: existingOrder.id,
          creditsPurchased: existingOrder.creditsPurchased,
          amountPaid: existingOrder.amountPaidCents / 100
        }
      });
    }

    // 处理订单完成
    return await processOrderCompletion(existingOrder, workspaceId, session.user.id!);

  } catch (err) {
    console.error('Payment verification error:', err);
    const error = err as Error & { statusCode?: number };
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: error.statusCode || 500 }
    );
  }
}

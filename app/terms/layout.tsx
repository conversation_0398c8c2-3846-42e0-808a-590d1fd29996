import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "服务条款 - HiveChat",
  description: "HiveChat 平台服务条款，了解我们的服务规范、用户责任、数据隐私保护等重要信息。",
  keywords: "Hive<PERSON><PERSON>, 服务条款, AI聊天机器人, 多租户, SaaS, 用户协议",
  robots: "index, follow",
  openGraph: {
    title: "服务条款 - HiveChat",
    description: "HiveChat 平台服务条款",
    type: "website",
  },
};

export default function TermsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}

"use client";
import React from 'react';
import { Typography, Card, Space, Anchor } from 'antd';
import { FileTextOutlined, SafetyOutlined, TeamOutlined, CloudOutlined } from '@ant-design/icons';
import Header from '@/app/components/Header';

const { Title, Text, Paragraph } = Typography;

export default function TermsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <Header />

      {/* Main Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 max-w-4xl">
        {/* Page Header */}
        <div className="text-center mb-8">
          <Title level={1} className="!text-3xl !font-bold !text-gray-900 !mb-4">
            <FileTextOutlined className="mr-3 text-blue-600" />
            服务条款
          </Title>
          <Text type="secondary" className="text-lg">
            HiveChat 平台服务条款
          </Text>
          <div className="mt-4">
            <Text type="secondary" className="text-sm">
              最后更新时间：2025年7月
            </Text>
          </div>
        </div>

        {/* Terms Content */}
        <Space direction="vertical" size="large" className="w-full">

          {/* 1. 服务描述和使用范围 */}
          <Card id="service-description" className="shadow-sm">
            <Title level={2} className="!text-xl !font-semibold !text-gray-800 !mb-4">
              <CloudOutlined className="mr-2 text-blue-600" />
              1. 服务描述和使用范围
            </Title>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>1.1 服务概述</strong><br />
              HiveChat 是一个多租户 SaaS 架构的 AI 聊天机器人平台，为个人用户和团队提供智能对话服务。我们的平台支持多种顶尖的人工智能模型，包括但不限于 OpenAI、Anthropic、Google Gemini、DeepSeek 等。
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>1.2 核心功能</strong><br />
              • 多 AI 模型集成和切换<br />
              • 工作空间创建和管理<br />
              • 团队协作和成员邀请<br />
              • 对话历史记录和管理<br />
              • API Key 配置和管理<br />
              • 搜索引擎集成<br />
              • MCP 集成
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>1.3 使用范围</strong><br />
              本服务仅供合法的商业和个人用途使用。用户不得将本服务用于任何违法、有害、威胁、滥用、骚扰、侵权、诽谤、粗俗、淫秽或其他不当的目的。
            </Paragraph>
          </Card>

          {/* 2. 用户责任和义务 */}
          <Card id="user-responsibilities" className="shadow-sm">
            <Title level={2} className="!text-xl !font-semibold !text-gray-800 !mb-4">
              <TeamOutlined className="mr-2 text-green-600" />
              2. 用户责任和义务
            </Title>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>2.1 账户安全</strong><br />
              • 用户有责任保护其账户信息的安全性和机密性<br />
              • 用户应立即通知我们任何未经授权使用其账户的情况<br />
              • 用户对其账户下发生的所有活动承担责任
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>2.2 API Key 管理</strong><br />
              • 用户负责妥善保管和配置第三方 AI 服务商的 API Key<br />
              • 用户应确保其 API Key 的合法性和有效性<br />
              • 因 API Key 泄露或滥用造成的损失由用户自行承担<br />
              • 用户应遵守各 AI 服务商的使用条款和限制
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>2.3 团队管理</strong><br />
              • 工作空间创建者对该工作空间的所有活动负责<br />
              • 邀请团队成员时应确保被邀请人的合法性<br />
              • 工作空间管理员有权管理成员权限和访问控制<br />
              • 用户应合理使用邀请功能，避免垃圾邀请
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>2.4 内容责任</strong><br />
              • 用户对其输入的所有内容承担完全责任<br />
              • 禁止输入违法、有害、侵权或不当的内容<br />
              • 禁止利用本服务进行任何形式的网络攻击或恶意行为
            </Paragraph>
          </Card>

          {/* 3. 数据隐私和安全 */}
          <Card id="data-privacy" className="shadow-sm">
            <Title level={2} className="!text-xl !font-semibold !text-gray-800 !mb-4">
              <SafetyOutlined className="mr-2 text-purple-600" />
              3. 数据隐私和安全
            </Title>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>3.1 数据收集</strong><br />
              我们收集的数据包括但不限于：<br />
              • 用户注册信息（邮箱、用户名等）<br />
              • 工作空间配置信息<br />
              • 对话记录和历史<br />
              • 使用统计和日志信息
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>3.2 数据使用</strong><br />
              • 我们仅将用户数据用于提供和改进服务<br />
              • 不会向第三方出售或泄露用户个人信息<br />
              • 对话内容仅在用户工作空间内可见<br />
              • 我们采用行业标准的安全措施保护用户数据
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>3.3 数据存储</strong><br />
              • 用户数据存储在安全的云服务器上<br />
              • 我们定期备份数据以防止数据丢失<br />
              • 用户可以随时导出其数据<br />
              • 账户删除后，相关数据将在合理期限内删除
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>3.4 多租户隔离</strong><br />
              • 我们确保不同工作空间之间的数据完全隔离<br />
              • 用户只能访问其有权限的工作空间数据<br />
              • 严格的权限控制确保数据安全
            </Paragraph>
          </Card>

          {/* 4. AI 服务商集成相关条款 */}
          <Card id="ai-integration" className="shadow-sm">
            <Title level={2} className="!text-xl !font-semibold !text-gray-800 !mb-4">
              4. AI 服务商集成相关条款
            </Title>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>4.1 第三方服务</strong><br />
              本平台集成了多个第三方 AI 服务商，包括：<br />
              • OpenAI (GPT 系列模型)<br />
              • Anthropic (Claude 系列模型)<br />
              • Google (Gemini 系列模型)<br />
              • DeepSeek 等其他 AI 服务商
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>4.2 服务可用性</strong><br />
              • AI 服务的可用性取决于第三方服务商<br />
              • 我们不保证第三方服务的持续可用性<br />
              • 第三方服务中断不构成我们的违约责任<br />
              • 我们会尽力提供多个备选服务商以提高可用性
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>4.3 费用和计费</strong><br />
              • 用户直接向 AI 服务商支付 API 使用费用<br />
              • 我们不收取额外的 AI 服务费用<br />
              • 用户应监控其 API 使用量和费用<br />
              • 因 API 超额使用产生的费用由用户承担
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>4.4 内容过滤</strong><br />
              • 各 AI 服务商可能对输入内容进行过滤<br />
              • 我们不对 AI 服务商的内容过滤策略负责<br />
              • 用户应遵守各服务商的使用政策
            </Paragraph>
          </Card>

          {/* 5. 工作空间和多租户相关规定 */}
          <Card id="workspace-rules" className="shadow-sm">
            <Title level={2} className="!text-xl !font-semibold !text-gray-800 !mb-4">
              5. 工作空间和多租户相关规定
            </Title>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>5.1 工作空间创建</strong><br />
              • 公测期间每个用户限制创建 1 个工作空间<br />
              • 工作空间 ID 必须唯一且符合命名规范<br />
              • 工作空间创建者自动成为该空间的所有者<br />
              • 工作空间名称和 ID 一经创建不可修改
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>5.2 成员管理</strong><br />
              • 工作空间所有者可以邀请其他用户加入<br />
              • 被邀请用户可以选择接受或拒绝邀请<br />
              • 工作空间管理员可以管理成员权限<br />
              • 用户可以主动退出工作空间（所有者除外）
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>5.3 权限控制</strong><br />
              • 不同角色拥有不同的操作权限<br />
              • 所有者：完全控制权限<br />
              • 管理员：管理权限（不包括删除工作空间）<br />
              • 普通成员：基本使用权限
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>5.4 数据隔离</strong><br />
              • 不同工作空间的数据完全隔离<br />
              • 用户只能访问其有权限的工作空间<br />
              • 工作空间删除后数据无法恢复
            </Paragraph>
          </Card>

          {/* 6. 免责声明 */}
          <Card id="disclaimer" className="shadow-sm">
            <Title level={2} className="!text-xl !font-semibold !text-gray-800 !mb-4">
              6. 免责声明
            </Title>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>6.1 服务可用性</strong><br />
              • 我们努力保证服务的稳定性，但不保证 100% 的可用性<br />
              • 因系统维护、升级或不可抗力导致的服务中断，我们不承担责任<br />
              • 我们会提前通知计划内的维护活动
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>6.2 AI 生成内容</strong><br />
              • AI 生成的内容仅供参考，不构成专业建议<br />
              • 用户应对 AI 生成内容的使用承担责任<br />
              • 我们不对 AI 生成内容的准确性、完整性或适用性做出保证<br />
              • 用户应独立验证 AI 生成内容的准确性
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>6.3 第三方服务</strong><br />
              • 我们不对第三方 AI 服务商的服务质量负责<br />
              • 第三方服务的中断、错误或损失不构成我们的责任<br />
              • 用户应直接与第三方服务商解决相关问题
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>6.4 数据安全</strong><br />
              • 虽然我们采用安全措施保护数据，但不能保证绝对安全<br />
              • 用户应定期备份重要数据<br />
              • 因网络攻击、系统故障等导致的数据丢失，我们的责任有限
            </Paragraph>
          </Card>

          {/* 7. 服务变更和终止条款 */}
          <Card id="service-changes" className="shadow-sm">
            <Title level={2} className="!text-xl !font-semibold !text-gray-800 !mb-4">
              7. 服务变更和终止条款
            </Title>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>7.1 服务变更</strong><br />
              • 我们保留随时修改、暂停或终止服务的权利<br />
              • 重大变更将提前 30 天通知用户<br />
              • 持续使用服务视为接受变更后的条款<br />
              • 用户如不同意变更，可以终止使用服务
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>7.2 账户终止</strong><br />
              • 用户可以随时删除其账户<br />
              • 我们可能因违规行为终止用户账户<br />
              • 账户终止前会提供数据导出机会<br />
              • 终止后的数据删除不可恢复
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>7.3 服务终止</strong><br />
              • 如果我们决定终止整个服务，将提前 90 天通知<br />
              • 用户将有充分时间导出其数据<br />
              • 我们会协助用户迁移到其他平台（如可能）
            </Paragraph>

            <Paragraph className="text-gray-700 leading-relaxed">
              <strong>7.4 条款变更</strong><br />
              • 本服务条款可能会定期更新<br />
              • 重要变更将通过邮件或平台通知<br />
              • 继续使用服务即表示同意更新后的条款<br />
              • 建议用户定期查看最新版本的服务条款
            </Paragraph>
          </Card>

          {/* 联系信息 */}
          <Card className="shadow-sm bg-blue-50 border-blue-200">
            <Title level={3} className="!text-lg !font-semibold !text-blue-800 !mb-3">
              联系我们
            </Title>
            <Paragraph className="text-blue-700 !mb-2">
              如果您对本服务条款有任何疑问或需要帮助，请通过以下方式联系我们：
            </Paragraph>
            <Paragraph className="text-blue-700 !mb-0">
              • 邮箱：<EMAIL><br />
              • 在线客服：通过平台内置客服系统<br />
              • 工作时间：周一至周五 9:00-18:00（北京时间）
            </Paragraph>
          </Card>

          {/* 法律声明 */}
          <Card className="shadow-sm bg-gray-50 border-gray-200">
            <Paragraph className="text-gray-600 text-sm !mb-0 text-center">
              本服务条款受中华人民共和国法律管辖。如发生争议，双方应友好协商解决；
              协商不成的，任何一方均可向有管辖权的人民法院提起诉讼。
              <br /><br />
              <strong>HiveChat 团队</strong>
            </Paragraph>
          </Card>

        </Space>
      </div>
    </div>
  );
}